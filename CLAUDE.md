# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Sootro is a React Native dating app built with Expo that focuses on connecting people based on shared interests. The app includes features like user authentication, profile management, group creation, and social interactions.

## Common Development Commands

### Development Setup
- Install dependencies: `yarn install`
- Start development server: `npx expo start`
- Reset cache: `npx expo start --reset-cache`
- Clear Expo cache: `expo start -c`

### Platform-Specific Development
- Run on iOS: `npx expo run:ios`
- Run on Android: `npx expo run:android`
- Run on web: `npx expo start --web`

### iOS Pod Management
- Install pods: `npm run pods`
- Clean and reinstall pods: `npm run clean-pods`
- Full clean install: `npm run clean-install`

### Firebase Development
- Start Firebase emulators: `npm run firebase` or `yarn firebase`
- Deploy Firebase functions: `firebase deploy --only functions`
- Local Firebase emulation: `firebase emulators:start`
- Debug functions: `firebase emulators:start --inspect-functions`

### Testing and Quality
- Run tests: `npm run test`
- Lint code: `npm run lint`

### Project Reset
- Reset to fresh project: `npm run reset-project`

## Architecture Overview

### Core Technologies
- **React Native 0.76.9** with **Expo 52** framework
- **TypeScript** for type safety
- **Expo Router** for file-based navigation
- **NativeWind** for Tailwind CSS styling
- **Firebase** for backend services (Auth, Storage, Functions)
- **Google Sign-In** for authentication

### App Structure
- **File-based routing**: Uses Expo Router with app directory structure
- **Context-based state management**: Authentication state via React Context
- **Component-driven architecture**: Reusable UI components
- **Utility-based organization**: Shared utilities for common functionality

### Key Directories

#### `/app/`
Main application screens and navigation:
- `_layout.tsx` - Root layout with session provider and global config
- `index.tsx` - Landing/onboarding screen
- `sign-in.tsx`, `sign-up.tsx` - Authentication screens
- `(app)/` - Protected app screens (groups, connections, profile, etc.)

#### `/components/`
Reusable UI components:
- Themed components (ThemedText, ThemedView)
- Custom form components (Checkbox)
- Profile components (UserProfileCard)
- Navigation components (TabBarIcon)

#### `/utils/`
Shared utility functions:
- `pushNotifications.ts` - Push notification setup and handling
- `appReview.ts` - In-app review prompting logic
- `imageUpload.ts` - Image handling utilities

#### `/config/`
Configuration files:
- `firebaseConfig.js` - Firebase initialization and setup

#### `/types/`
TypeScript type definitions:
- `types.ts` - App-specific type definitions

### Firebase Integration

#### Authentication
- Google Sign-In integration
- Email/password authentication
- Session persistence with AsyncStorage

#### Cloud Functions
- Backend API functions in `/functions/` directory
- Example endpoint: `getGroups` function
- Local development with Firebase emulators

#### Storage
- Firebase Storage for image uploads
- Profile pictures and media handling

### State Management
- **Authentication Context**: Global auth state via `ctx.tsx`
- **Session Storage**: Persistent auth using `useStorageState` hook
- **Local State**: Component-level state with React hooks

### Styling
- **NativeWind**: Tailwind CSS for React Native
- **Consistent theming**: ThemedText and ThemedView components
- **Responsive design**: Mobile-first approach

### Push Notifications
- Expo Notifications for cross-platform push notifications
- Firebase Cloud Messaging integration
- Device token management and registration

### App Store Presence
- iOS bundle ID: `com.alexgrande.sootro`
- Android package: `com.alexgrande.sootro`
- Version 1.0.5 with OTA updates via Expo

## Development Guidelines

### Code Style
- Use TypeScript for all new files
- Follow React Native and Expo best practices
- Use NativeWind classes instead of StyleSheet
- Implement proper error handling with try-catch blocks

### Authentication Flow
- Check session state using `useSession()` hook
- Protected routes automatically redirect to sign-in
- Store user data and tokens securely

### Navigation
- Use Expo Router's file-based routing
- Protected routes in `(app)` group
- Implement proper navigation guards

### Firebase Development
- Test locally with Firebase emulators before deploying
- Use environment-appropriate Firebase configuration
- Handle offline scenarios gracefully

### Performance
- Optimize images for mobile devices
- Use lazy loading for large lists
- Implement proper memory management for media

### Testing
- Write unit tests for utility functions
- Test authentication flows thoroughly
- Validate Firebase integration in staging environment

## Important Configuration Files

### `/app.json`
Expo configuration with:
- App metadata and versioning
- Platform-specific settings (iOS/Android)
- Plugin configurations
- OTA update settings

### `/package.json`
Dependencies and scripts including:
- React Native and Expo dependencies
- Firebase SDKs
- Development and build tools

### Native Platform Files
- `/ios/` - iOS-specific configuration and build files
- `/android/` - Android-specific configuration and build files

## Troubleshooting

### Common Issues
- **Node process conflicts**: Use `lsof -i :8081` to find and kill running processes
- **Pod installation issues**: Run `npm run clean-pods` for complete reset
- **Cache issues**: Use `npx expo start --reset-cache` or `expo start -c`
- **Build issues**: Run `npm run clean-install` for complete dependency reset

### Firebase Debugging
- Use Firebase emulator suite for local development
- Check Firebase console for function logs
- Verify Firebase configuration and API keys

### Platform-Specific Issues
- **iOS**: Ensure proper provisioning profiles and certificates
- **Android**: Check Google Services configuration
- **Expo**: Verify EAS configuration for builds and updates