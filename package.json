{"name": "sootro", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start && node --inspect=0.0.0.0:9229 node_modules/@google-cloud/functions-framework --target=getGroups --source=functions/src/index.ts", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "pods": "cd ios && pod install && cd ..", "clean-pods": "cd ios && pod deintegrate && rm -rf Pods && rm Podfile.lock && pod install && cd ..", "firebase": "npm run serve --prefix functions", "clean-install": "rm -rf node_modules && npm install && npx expo prebuild --clean && npm run pods"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@google-cloud/functions-framework": "^3.4.5", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-firebase/app": "^23.1.0", "@react-native-firebase/app-check": "^23.1.0", "@react-native-firebase/auth": "^23.0.1", "@react-native-firebase/messaging": "^23.0.1", "@react-native-firebase/storage": "^23.0.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/bottom-tabs": "^7.0.14", "@react-navigation/native": "^7.0.9", "@react-navigation/native-stack": "^7.1.10", "@react-navigation/stack": "^7.0.14", "dotenv": "^16.5.0", "expo": "^52.0.46", "expo-build-properties": "~0.13.1", "expo-constants": "~17.0.3", "expo-dev-client": "~5.0.20", "expo-device": "^7.0.3", "expo-font": "~13.0.1", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.0.3", "expo-linking": "~7.0.3", "expo-notifications": "~0.29.14", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.0", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.1", "firebase": "^11.1.0", "firebase-functions": "^6.4.0", "long": "^5.2.3", "metro-react-native-babel-transformer": "^0.77.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-deck-swiper": "^2.0.17", "react-native-gesture-handler": "~2.20.2", "react-native-in-app-review": "^4.3.5", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-popup-menu": "^0.16.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-toast-message": "^2.2.1", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.10", "tailwind-rn": "^4.2.0", "tailwindcss": "^3.4.15", "watchman": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest", "@types/jest": "^29.5.12", "@types/node": "^24.1.0", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.7", "concurrently": "^9.1.2", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.2.0", "source-map-support": "^0.5.21", "typescript": "~5.3.3"}, "private": true}