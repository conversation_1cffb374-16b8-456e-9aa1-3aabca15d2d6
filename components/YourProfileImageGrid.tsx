import React from 'react';
import { View, Image, FlatList, StyleSheet } from 'react-native';
import Colors from '@/constants/Colors';

interface ImageGridProps {
  photos: string[];
  numOfImages: number;
}

export default function ImageGrid({ photos, numOfImages }: ImageGridProps) {
  // Convert the string URLs into objects with an ID and src
  const imagesForFlatList = photos.slice(0, numOfImages).map((url, index) => ({
    id: index.toString(),
    src: url,
  }));

  return (
    <FlatList
      style={styles.gridContainer}
      data={imagesForFlatList}
      renderItem={({ item }) => (
        <View style={styles.imageWrapper}>
          <Image source={{ uri: item.src }} style={styles.image} />
        </View>
      )}
      keyExtractor={(item) => item.id}
      numColumns={3}
      contentContainerStyle={styles.container}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'flex-start',
  },
  gridContainer: {
    marginBottom: 20,
  },
  imageWrapper: {
    width: '30%',
    aspectRatio: 1,
    margin: '1.5%',
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.backgroundSecondary,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
});