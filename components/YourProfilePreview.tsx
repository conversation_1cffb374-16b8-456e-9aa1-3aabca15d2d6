import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View, TouchableOpacity} from 'react-native';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {doc, getDoc, getFirestore} from 'firebase/firestore';
import {getAuth} from 'firebase/auth';
import YourProfileImageGrid from '@/components/YourProfileImageGrid';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Colors from '@/constants/Colors';

// Define navigation params type
type RootStackParamList = {
    EditProfile: undefined;
    // Add other routes as needed
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'EditProfile'>;

export default function YourProfilePreview() {
    const navigation = useNavigation<NavigationProp>();
    const [profileName, setProfileName] = useState('');
    const [age, setAge] = useState(0);
    const [images, setImages] = useState<string[]>([]);
    const profileSummary = [profileName, age].filter(Boolean).join(', ');

    function calculatePreciseAge(birth: Date): string {
        const now = new Date();
        let years = now.getFullYear() - birth.getFullYear();
        let months = now.getMonth() - birth.getMonth();
        let days = now.getDate() - birth.getDate();

        if (days < 0) {
            months--;
            const prevMonDays = new Date(
                now.getFullYear(),
                now.getMonth(),
                0,
            ).getDate();
            days = prevMonDays + days;
        }

        if (months < 0) {
            years--;
            months = 12 + months;
        }

        return `${years} years old`;
    }

    const fetchProfileData = async () => {
        const auth = getAuth();
        const user = auth.currentUser;
        if (!user) return;

        const db = getFirestore();
        const userDoc = doc(db, 'users', user.uid);
        const docSnap = await getDoc(userDoc);

        if (docSnap.exists()) {
            const data = docSnap.data();
            setProfileName(data.name || '');

            const birthDate = new Date(data.birthDate);
            const age = calculatePreciseAge(birthDate);
            setAge(parseInt(age));

            // Fetch images array from the user document
            const userImages = Array.isArray(data.images) ? data.images : [];
            setImages(userImages);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            fetchProfileData();
        }, []),
    );

    useEffect(() => {
        return getAuth().onAuthStateChanged(user => {
            fetchProfileData();
        });
    }, []);

    return (
        <View style={styles.container}>
            <View style={styles.topContainer}>
                <Text style={styles.title}>Your Profile</Text>
                <TouchableOpacity
                    style={styles.editButton}
                    onPress={() => navigation.navigate('EditProfile')}
                >
                    <Text style={styles.editButtonText}>Edit</Text>
                </TouchableOpacity>
            </View>

            <Text style={styles.subtitle}>
                {profileSummary ? profileSummary : 'Please fill your profile!'}
            </Text>
            {/* Pass images to YourProfileImageGrid */}
            <YourProfileImageGrid photos={images} numOfImages={3} />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginBottom: 24,
        width: '100%',
    },
    topContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 0,
        width: '100%',
        marginBottom: 8,
    },
    title: {
        fontSize: 24,
        fontWeight: '600',
        color: Colors.text,
    },
    subtitle: {
        fontSize: 16,
        color: Colors.textSecondary,
        marginBottom: 16,
    },
    editButton: {
        backgroundColor: Colors.primary,
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 10,
    },
    editButtonText: {
        color: Colors.textLight,
        fontWeight: '600',
        fontSize: 14,
    },
});
