import {auth, functions} from '@/config/firebaseConfig';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {httpsCallable} from 'firebase/functions';
import {callFirebaseFunction} from '@/utils/firebaseUtils';
import React, {useCallback, useEffect, useState} from 'react';
import {
    ActivityIndicator,
    Alert,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    ScrollView,
    Dimensions,
} from 'react-native';
import {onAuthStateChanged} from 'firebase/auth';
import {Group} from '@/types/types';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Colors from '@/constants/Colors';

// Define the navigation params type
type RootStackParamList = {
    Group: {
        groupId: string;
        groupName: string;
        groupLocation: string;
    };
    Groups: undefined;
    // Add other routes as needed
};

// Create a typed navigation hook
type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'Group'>;

export default function YourGroupsPreview() {
    const [groups, setGroups] = useState<Group[]>([]);
    const navigation = useNavigation<NavigationProp>();
    const {currentUser: user} = auth;
    const [loading, setLoading] = useState(true);
    const [hasLoadedOnce, setHasLoadedOnce] = useState(false);

    useEffect(() => {
        // Listen for auth state changes
        const unsubscribe = onAuthStateChanged(auth, user => {
            setLoading(true);
            if (user) {
                fetchGroups(user.uid);
            }
        });

        // Cleanup subscription
        return () => unsubscribe();
    }, []);

    const fetchGroups = async (uid: string) => {
        try {
            const groupsData = await callFirebaseFunction<Group[]>(functions, 'yourGroups', {}, user);
            setGroups(groupsData);
            setHasLoadedOnce(true);
        } catch (error: any) {
            console.error('Error fetching groups: ', error);
            Alert.alert('Error', error.message || 'Failed to fetch groups.');
        }
        setLoading(false);
    };

    useFocusEffect(
        useCallback(() => {
            if (!hasLoadedOnce) {
                // Only show loading spinner on first load
                setLoading(true);
                if (user) {
                    fetchGroups(user.uid);
                }
            } else {
                // Silent refresh on subsequent visits
                if (user) {
                    callFirebaseFunction<Group[]>(functions, 'yourGroups', {}, user)
                        .then(groupsData => {
                            setGroups(groupsData);
                        })
                        .catch(error => {
                            console.error('Error silently refreshing groups: ', error);
                        });
                }
            }
        }, [user, hasLoadedOnce]),
    );

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Your Groups</Text>
            {loading ? (
                <ActivityIndicator size="large" color={Colors.primary} style={styles.loader} />
            ) : (
                <>
                    {groups.length > 0 ? (
                        <ScrollView 
                            style={styles.scrollContainer}
                            showsVerticalScrollIndicator={true}
                            contentContainerStyle={styles.scrollContentContainer}
                        >
                            {groups.map(item => (
                                <TouchableOpacity
                                    key={item.id}
                                    style={styles.groupItem}
                                    onPress={() => {
                                        navigation.navigate('Group', {
                                            groupId: item.id,
                                            groupName: item.name,
                                            groupLocation: item.location,
                                        });
                                    }}>
                                    <Text style={styles.groupName}>{item.name}</Text>
                                </TouchableOpacity>
                            ))}
                        </ScrollView>
                    ) : (
                        <View style={styles.noGroupsContainer}>
                            <Text style={styles.noGroupsText}>No groups joined yet</Text>
                            <TouchableOpacity 
                                style={styles.findGroupButton}
                                onPress={() => navigation.navigate('Groups')}
                            >
                                <Text style={styles.findGroupButtonText}>Find Groups</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </>
            )}
        </View>
    );
}

const { height } = Dimensions.get('window');
const maxHeight = height * 0.65; // 65% of screen height instead of 35%

const styles = StyleSheet.create({
    container: {
        width: '100%',
        marginBottom: 20,
        flex: 1,
    },
    scrollContainer: {
        maxHeight: maxHeight,
        width: '100%',
        flexGrow: 1,
    },
    scrollContentContainer: {
        paddingRight: 10,
        flexGrow: 1,
    },
    groupItem: {
        padding: 16,
        marginBottom: 10,
        width: '100%',
        backgroundColor: Colors.primaryLight,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: Colors.borderAccent,
    },
    groupInfoContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 8,
    },
    title: {
        fontSize: 24,
        marginBottom: 16,
        fontWeight: '600',
        color: Colors.text,
    },
    groupName: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
    },
    groupInfo: {
        fontSize: 14,
        color: Colors.textSecondary,
    },
    groupLocation: {
        fontSize: 14,
        color: Colors.primaryDark,
        textAlign: 'right',
    },
    loader: {
        marginVertical: 20,
    },
    noGroupsContainer: {
        alignItems: 'center',
        padding: 24,
        backgroundColor: Colors.primaryLight,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: Colors.borderAccent,
    },
    noGroupsText: {
        color: Colors.textSecondary,
        marginBottom: 16,
        fontSize: 16,
    },
    findGroupButton: {
        backgroundColor: Colors.primary,
        paddingVertical: 10,
        paddingHorizontal: 16,
        borderRadius: 10,
    },
    findGroupButtonText: {
        color: Colors.textLight,
        fontWeight: '600',
        fontSize: 14,
    },
});
