import React, {useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {
    Animated,
    Dimensions,
    Easing,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {getFunctions, httpsCallable} from 'firebase/functions';
import {getAuth} from 'firebase/auth';
import Toast from 'react-native-toast-message';

interface UserProfileCardProps {
    profile: {
        id: string;
        profileName: string;
        birthDate: number; // storing a JS timestamp
        location: string;
        images: string | string[];
        description: string;
    };
    onLike: (id: string) => void;
    onRemove: (id: string) => void;
}

// Precise age calculation
function calculatePreciseAge(birth: Date): string {
    const now = new Date();
    let years = now.getFullYear() - birth.getFullYear();
    let months = now.getMonth() - birth.getMonth();
    let days = now.getDate() - birth.getDate();

    if (days < 0) {
        months--;
        const prevMonDays = new Date(
            now.getFullYear(),
            now.getMonth(),
            0,
        ).getDate();
        days = prevMonDays + days;
    }
    if (months < 0) {
        years--;
        months = 12 + months;
    }

    return `${years} years old`;
}

const UserProfileCard: React.FC<UserProfileCardProps> = ({
    profile,
    onLike,
    onRemove,
}) => {
    const navigation = useNavigation();
    const [cardAnim] = useState(new Animated.Value(1));
    const auth = getAuth();
    const currentUser = auth.currentUser;
    const functions = getFunctions();

    const handleLikeProfile = async (user: UserProfileCardProps['profile']) => {
        if (!currentUser) return;

        const likeProfile = httpsCallable(functions, 'likeProfile');

        try {
            const response = await likeProfile({
                userId: currentUser.uid,
                likedUserId: user.id,
            });

            const responseData = response.data as {match: boolean};

            // Only show toast if it's a match
            if (responseData.match) {
                Toast.show({
                    type: 'success',
                    text1: 'You have a match!',
                    text2: 'Go to your Connections to start chatting.',
                });
            }
        } catch (error) {
            console.error('Error liking profile: ', error);
        }
    };

    const handleRemoveProfile = (user: object) => {
        const dislikeProfile = httpsCallable(functions, 'dislikeProfile');

        try {
            dislikeProfile({userId: currentUser.uid, dislikedUserId: user.id});
        } catch (error) {
            console.error('Error disliking profile: ', error);
        }
    };

    const userImages = Array.isArray(profile.images)
        ? profile.images
        : [profile.images];
    let displayedAge = '';
    if (profile.birthDate) {
        const birth = new Date(profile.birthDate);
        displayedAge = calculatePreciseAge(birth);
    }

    // Animate "grow then fade out"
    const handleLike = (profile: object) => {
        Animated.sequence([
            Animated.timing(cardAnim, {
                toValue: 1.5,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(cardAnim, {
                toValue: 0,
                duration: 300,
                easing: Easing.in(Easing.quad),
                useNativeDriver: true,
            }),
        ]).start(() => {
            onLike(profile.id);
            handleLikeProfile(profile);
        });
    };

    // Animate "shrink then fade out"
    const handleRemove = (profile: object) => {
        Animated.sequence([
            Animated.timing(cardAnim, {
                toValue: 0.9,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(cardAnim, {
                toValue: 0,
                duration: 300,
                easing: Easing.in(Easing.quad),
                useNativeDriver: true,
            }),
        ]).start(() => {
            onRemove(profile.id);
            handleRemoveProfile(profile);
        });
    };

    // Interpolate opacity from the same cardAnim
    const cardOpacity = cardAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 1],
    });

    const animatedStyle = {
        transform: [{scale: cardAnim}],
        opacity: cardOpacity,
    };

    const handleSeeChat = (id: string) => {
        navigation.navigate('Connection', {
            userId: id,
        });
    };

    return (
        <Animated.View style={[styles.card, animatedStyle]}>
            <ScrollView contentContainerStyle={styles.infoContainer}>
                {/* Show first image if available */}
                <View style={styles.imageWrapper}>
                    {userImages[0] && (
                        <Image
                            source={{uri: userImages[0]}}
                            style={styles.photo}
                        />
                    )}
                </View>
                <View style={styles.buttonContainer}>
                    <TouchableOpacity
                        onPress={() => handleRemove(profile)}
                        style={styles.removeButton}>
                        <Text style={styles.buttonText}>-</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => handleLike(profile)}
                        style={styles.likeButton}>
                        <Text style={styles.buttonText}>+</Text>
                    </TouchableOpacity>
                </View>
                <Text style={styles.name}>{profile.name}</Text>
                {!!displayedAge && (
                    <Text style={styles.age}>{displayedAge}</Text>
                )}
                <Text style={styles.location}>{profile.location}</Text>
                <Text style={styles.description}>{profile.description}</Text>

                {/* Render additional images beyond the first */}
                {userImages.slice(1).map((imageUrl, index) => (
                    <Image
                        key={index}
                        source={{uri: imageUrl}}
                        style={styles.photo}
                    />
                ))}
            </ScrollView>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    imageWrapper: {
        width: '100%',
        height: 300,
        marginBottom: 20,
    },
    card: {
        width: Dimensions.get('window').width,
        backgroundColor: 'white',
        // Do not set height: "100%" so the ScrollView can scroll
    },
    infoContainer: {
        flexGrow: 1,
        padding: 10,
    },
    photo: {
        width: '100%',
        height: 300,
        marginBottom: 20,
    },
    name: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 5,
    },
    age: {
        fontSize: 18,
        color: '#888',
        marginBottom: 5,
    },
    location: {
        fontSize: 18,
        color: '#666',
        marginBottom: 10,
    },
    description: {
        fontSize: 16,
        color: '#666',
        marginTop: 10,
        marginBottom: 50,
    },
    buttonContainer: {
        flexDirection: 'row',
        marginTop: -50,
        marginBottom: 20,
        width: '100%',
        justifyContent: 'center',
    },
    likeButton: {
        width: 60,
        height: 60,
        borderRadius: 30,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#E8E8E8',
    },
    removeButton: {
        width: 60,
        height: 60,
        borderRadius: 30,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#E8E8E8',
        marginEnd: 20,
    },
    buttonText: {
        color: 'black',
        fontSize: 24,
        fontWeight: 'bold',
    },
});

export default UserProfileCard;
