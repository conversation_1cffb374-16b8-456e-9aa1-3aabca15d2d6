import React from 'react';
import { Text, StyleSheet, Image, View } from 'react-native';

interface TitleTaglineProps {
    showTagline?: boolean;
}

export default function TitleTagline({ showTagline = true }: TitleTaglineProps) {
    return (
        <View style={styles.container}>
            <Image 
                source={require('../assets/images/sootro_logo.png')} 
                style={styles.logo}
                resizeMode="contain"
            />
            {showTagline && (
                <Text style={styles.tagline}>
                    Connect with people based on your interests for friendship and more
                </Text>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
    },
    tagline: {
        fontSize: 18,
        color: '#666',
        marginBottom: 10,
        textAlign: 'center',
    },
    logo: {
        width: 100,
        height: 50,
        marginBottom: 10,
    },
});