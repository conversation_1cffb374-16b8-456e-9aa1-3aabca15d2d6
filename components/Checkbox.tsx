import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

const Checkbox = ({
    value,
    onValueChange,
}: {
    value: boolean;
    onValueChange: (value: boolean) => void;
}) => {
    return (
        <TouchableOpacity
            style={[styles.checkbox, value ? styles.checkboxChecked : {}]}
            onPress={() => onValueChange(!value)}
            activeOpacity={0.7}>
            {value && (
                <View style={styles.checkmark}>
                    <Text style={styles.checkmarkText}>✓</Text>
                </View>
            )}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    checkbox: {
        width: 20,
        height: 20,
        borderWidth: 1,
        borderColor: '#C09B72',
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'white',
    },
    checkboxChecked: {
        backgroundColor: '#C09B72',
    },
    checkmark: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    checkmarkText: {
        color: 'white',
        fontSize: 12,
        fontWeight: 'bold',
    },
});

export default Checkbox;
