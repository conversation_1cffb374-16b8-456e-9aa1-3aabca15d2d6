// Removed unused import
const admin = require('firebase-admin');
const firestore = require('firebase-admin/firestore');
const {FieldValue} = firestore;
const {VertexAI} = require('@google-cloud/vertexai');
const {onCall, HttpsError} = require('firebase-functions/v2/https');

admin.initializeApp();
const db = admin.firestore();

interface GetGroupsResponse {
    id: string;
    users: number;
    isMember: boolean;
    [key: string]: any;
}


const getGroups = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any): Promise<GetGroupsResponse[]> => {
    const {data} = request;
    const {limit = 10} = data || {};

    // Verify user is authenticated using v2 API
    if (!request.auth || !request.auth.uid) {
        throw new HttpsError(
            'unauthenticated',
            'The function must be called while authenticated.'
        );
    }

    const userId = request.auth.uid;        try {
            console.log('=== getGroups function called ===');
            console.log('Authenticated userId:', userId);
            console.log('limit:', limit);
            // Get the user's keywords from their profile
            const userDoc = await db.collection('users').doc(userId).get();
            
            if (!userDoc.exists) {
                console.log('User document not found, returning empty groups');
                return [];
            }

            const userData = userDoc.data();
            const userKeywords = userData?.keywords || [];
            const userGroupIds = userData?.groups || [];
            const userLocation = userData?.location || '';

            console.log('User keywords:', userKeywords);
            console.log('User groups:', userGroupIds);
            console.log('User location:', userLocation);

            if (userKeywords.length === 0) {
                // If no keywords, prioritize groups by location first, then random selection
                console.log('No user keywords found, returning location-based groups');
                let groups: GetGroupsResponse[] = [];

                // First, try to get groups from user's location if available
                if (userLocation && userLocation.trim()) {
                    console.log('Fetching local groups for location:', userLocation);
                    const localGroupsSnapshot = await db.collection('Groups')
                        .where('location', '>=', userLocation.toLowerCase())
                        .where('location', '<=', userLocation.toLowerCase() + '\uf8ff')
                        .limit(Math.min(limit, 20))
                        .get();

                    for (const doc of localGroupsSnapshot.docs) {
                        const groupData = doc.data();
                        const groupUsersSnapshot = await db
                            .collection(`Groups/${doc.id}/Group_Users`)
                            .get();
                        const groupUsersCount = groupUsersSnapshot.size;
                        const isMember = userGroupIds.includes(doc.id);

                        groups.push({
                            id: doc.id,
                            ...groupData,
                            users: groupUsersCount,
                            isMember: isMember,
                        });
                    }
                }

                // Fill remaining slots with random groups if needed
                if (groups.length < limit) {
                    const remainingLimit = limit - groups.length;
                    const existingIds = new Set(groups.map(g => g.id));
                    
                    const randomGroupsSnapshot = await db.collection('Groups')
                        .limit(remainingLimit * 2) // Get extra to account for duplicates
                        .get();

                    for (const doc of randomGroupsSnapshot.docs) {
                        if (existingIds.has(doc.id) || groups.length >= limit) continue;

                        const groupData = doc.data();
                        const groupUsersSnapshot = await db
                            .collection(`Groups/${doc.id}/Group_Users`)
                            .get();
                        const groupUsersCount = groupUsersSnapshot.size;
                        const isMember = userGroupIds.includes(doc.id);

                        groups.push({
                            id: doc.id,
                            ...groupData,
                            users: groupUsersCount,
                            isMember: isMember,
                        });
                    }
                }

                return groups;
            }

            // For users with keywords, use a more efficient approach
            console.log('Finding groups based on keywords and location');
            const matchedGroups: Array<{group: any, score: number, isLocalGroup: boolean}> = [];
            let processedCount = 0;
            const maxProcessLimit = 100; // Limit how many groups we process

            // First pass: Get local groups if user has a location
            if (userLocation && userLocation.trim()) {
                console.log('First pass: Getting local groups');
                const localGroupsSnapshot = await db.collection('Groups')
                    .where('location', '>=', userLocation.toLowerCase())
                    .where('location', '<=', userLocation.toLowerCase() + '\uf8ff')
                    .limit(50)
                    .get();

                for (const doc of localGroupsSnapshot.docs) {
                    if (processedCount >= maxProcessLimit) break;
                    
                    const groupData = doc.data();
                    const groupSearchTokens = groupData.searchTokens || [];
                    
                    // Calculate keyword match score
                    let score = 0;
                    for (const userKeyword of userKeywords) {
                        for (const token of groupSearchTokens) {
                            if (token.toLowerCase().includes(userKeyword.toLowerCase()) || 
                                userKeyword.toLowerCase().includes(token.toLowerCase())) {
                                score += 1;
                            }
                        }
                    }

                    const groupUsersSnapshot = await db
                        .collection(`Groups/${doc.id}/Group_Users`)
                        .get();
                    const groupUsersCount = groupUsersSnapshot.size;
                    const isMember = userGroupIds.includes(doc.id);

                    matchedGroups.push({
                        group: {
                            id: doc.id,
                            ...groupData,
                            users: groupUsersCount,
                            isMember: isMember,
                        },
                        score: score,
                        isLocalGroup: true
                    });
                    
                    processedCount++;
                }
            }

            // Second pass: Get additional groups that match keywords (if we need more)
            if (matchedGroups.length < limit && processedCount < maxProcessLimit) {
                console.log('Second pass: Getting keyword-matched groups');
                const existingIds = new Set(matchedGroups.map(item => item.group.id));
                const additionalLimit = Math.min(maxProcessLimit - processedCount, 50);
                
                const additionalGroupsSnapshot = await db.collection('Groups')
                    .limit(additionalLimit * 2) // Get extra to account for filtering
                    .get();

                for (const doc of additionalGroupsSnapshot.docs) {
                    if (processedCount >= maxProcessLimit || matchedGroups.length >= limit * 2) break;
                    if (existingIds.has(doc.id)) continue;
                    
                    const groupData = doc.data();
                    const groupSearchTokens = groupData.searchTokens || [];
                    
                    // Calculate keyword match score
                    let score = 0;
                    for (const userKeyword of userKeywords) {
                        for (const token of groupSearchTokens) {
                            if (token.toLowerCase().includes(userKeyword.toLowerCase()) || 
                                userKeyword.toLowerCase().includes(token.toLowerCase())) {
                                score += 1;
                            }
                        }
                    }

                    // Only include if there's a keyword match
                    if (score > 0) {
                        const groupUsersSnapshot = await db
                            .collection(`Groups/${doc.id}/Group_Users`)
                            .get();
                        const groupUsersCount = groupUsersSnapshot.size;
                        const isMember = userGroupIds.includes(doc.id);

                        matchedGroups.push({
                            group: {
                                id: doc.id,
                                ...groupData,
                                users: groupUsersCount,
                                isMember: isMember,
                            },
                            score: score,
                            isLocalGroup: false
                        });
                    }
                    
                    processedCount++;
                }
            }

            // Sort by location first (local groups at top), then by score (highest first)
            matchedGroups.sort((a, b) => {
                if (a.isLocalGroup && !b.isLocalGroup) return -1;
                if (!a.isLocalGroup && b.isLocalGroup) return 1;
                return b.score - a.score;
            });
            const topGroups = matchedGroups.slice(0, limit).map(item => item.group);

            console.log(`Found ${matchedGroups.length} matching groups, returning top ${topGroups.length}`);

            // If we don't have enough matches, fill with random groups
            if (topGroups.length < limit) {
                const existingIds = new Set(topGroups.map(g => g.id));
                const additionalGroupsSnapshot = await db.collection('Groups')
                    .limit(limit - topGroups.length + 5) // Get a few extra in case some are duplicates
                    .get();

                for (const doc of additionalGroupsSnapshot.docs) {
                    if (existingIds.has(doc.id) || topGroups.length >= limit) continue;

                    const groupData = doc.data();
                    const groupUsersSnapshot = await db
                        .collection(`Groups/${doc.id}/Group_Users`)
                        .get();
                    const groupUsersCount = groupUsersSnapshot.size;
                    const isMember = userGroupIds.includes(doc.id);

                    topGroups.push({
                        id: doc.id,
                        ...groupData,
                        users: groupUsersCount,
                        isMember: isMember,
                    });
                }
            }

            return topGroups;

        } catch (error) {
            console.error('Error in getGroups function:', error);
            throw new HttpsError(
                'unknown',
                'Failed to fetch recommended groups.',
            );
        }
    },
);

// Remove the GetGroupsData interface - no longer needed

const yourGroups = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
    // Verify user is authenticated using v2 API
    if (!request.auth || !request.auth.uid) {
        throw new HttpsError(
            'unauthenticated',
            'The function must be called while authenticated.'
        );
    }

    const userId = request.auth.uid;
        
        try {
            console.log('=== yourGroups function called ===');
            console.log('Authenticated userId:', userId);
            // Get the user's groups array from their document
            const userDoc = await db.collection('users').doc(userId).get();
            
            if (!userDoc.exists) {
                return [];
            }
            
            const userData = userDoc.data();
            const userGroupIds = userData?.groups || [];
            
            if (userGroupIds.length === 0) {
                return [];
            }
            
            // Fetch only the groups that the user is a member of
            const groups = [];
            
            for (const groupId of userGroupIds) {
                try {
                    // Get group data
                    const groupDoc = await db.collection('Groups').doc(groupId).get();
                    
                    if (groupDoc.exists) {
                        const groupData = groupDoc.data();
                        
                        // Get user count for this group
                        const groupUsersSnapshot = await db
                            .collection(`Groups/${groupId}/Group_Users`)
                            .get();
                        
                        groups.push({
                            id: groupId,
                            ...groupData,
                            users: groupUsersSnapshot.size,
                        });
                    }
                } catch (error) {
                    console.error(`Error fetching group ${groupId}:`, error);
                    // Continue with other groups even if one fails
                }
            }
            
            return groups;
        } catch (error) {
            console.error('Error in yourGroups function:', error);
            throw new HttpsError(
                'unknown',
                'Failed to fetch your groups.',
            );
        }
    },
);

// Removed unused interface

const likeProfile = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
    const {data} = request;
    const {userId, likedUserId} = data;
    const serverTime = FieldValue.serverTimestamp();

    // Verify user is authenticated using v2 API
    if (!request.auth || !request.auth.uid) {
        throw new HttpsError(
            'unauthenticated',
            'The function must be called while authenticated.',
        );
    }

    if (!userId) {
        throw new HttpsError(
            'unauthenticated',
            'The function must be called while authenticated.',
        );
    }

    if (!likedUserId) {
        throw new HttpsError(
            'invalid-argument',
            'The function must be called with a valid likedUserId.',
        );
    }

    try {
        // Store the like in the user's likes collection
        const likeDocRef = db
            .collection('users')
            .doc(userId)
            .collection('likes')
            .doc(likedUserId);

        await likeDocRef.set({
            userId: likedUserId, // Store who was liked
            timestamp: serverTime,
        });

        // Check if this is a mutual like
        const reciprocalLikeSnap = await db
            .collection('users')
            .doc(likedUserId)
            .collection('likes')
            .doc(userId)
            .get();

        // If it's a match, create a chat and remove users from each other's likes
        if (reciprocalLikeSnap.exists) {
            // Generate a reproducible ID for the chat: sort the two user IDs and join them
            const sortedParticipantIds = [userId, likedUserId].sort();
            const chatDocId = sortedParticipantIds.join('_');

            // Create the chat
            const chatRef = db.collection('chats').doc(chatDocId);
            await chatRef.set({
                participants: sortedParticipantIds,
                createdAt: serverTime,
            });

            // Remove users from each other's likes lists
            // 1. Delete the like in current user's likes collection
            await likeDocRef.delete();

            // 2. Delete the reciprocal like
            await db
                .collection('users')
                .doc(likedUserId)
                .collection('likes')
                .doc(userId)
                .delete();

            // Return match with chatId
            return {
                success: true,
                match: true,
                chatId: chatDocId,
            };
        }

        // Return no match
        return {
            success: true,
            match: false,
        };
    } catch (error) {
        console.error('Error liking profile: ', error);
        throw new HttpsError(
            'unknown',
            'Failed to like profile.',
        );
    }
});

// Removed unused interface

const getGroup = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
    const {data} = request;
    const {groupId, currentUserId, page = 1, limit = 10} = data;
    
    // Verify user is authenticated using v2 API
    if (!request.auth || !request.auth.uid) {
        throw new HttpsError(
            'unauthenticated',
            'The function must be called while authenticated.',
        );
    }

    if (!groupId || !currentUserId) {
        throw new HttpsError(
            'invalid-argument',
            'groupId and currentUserId are required.',
        );
    }

    try {
        const groupUsersSnapshot = await db
            .collection(`Groups/${groupId}/Group_Users`)
            .where('id', '!=', currentUserId)
            .get();

        const allGroupUsers = [];

        for (const userDoc of groupUsersSnapshot.docs) {
            const userData = userDoc.data();

            // Check if user was disliked
            const dislikedUsers = await db
                .collection(`users/${currentUserId}/dislikes`)
                .where('id', '==', userData.id)
                .get();

            if (!dislikedUsers.empty) {
                // If found any disliked doc, skip this user
                continue;
            }
            // TODO: set a standard Chat ID format
            const chatDocId1 = `${currentUserId}_${userData.id}`;
            const chatDocId2 = `${userData.id}_${currentUserId}`;

            const chatSnap1 = await db
                .collection('chats')
                .doc(chatDocId1)
                .get();
            const chatSnap2 = await db
                .collection('chats')
                .doc(chatDocId2)
                .get();

            if (chatSnap1.exists || chatSnap2.exists) {
                continue;
            }

            const userMetaDoc = await db
                .collection('users')
                .doc(userDoc.id)
                .get();

            const userMeta = userMetaDoc.data();
            if (userMeta) {
                userMeta.id = userData.id;
                allGroupUsers.push(userMeta);
            }
        }

        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;

        return allGroupUsers.slice(startIndex, endIndex);
    } catch (error) {
        console.error('Error fetching group users:', error);
        throw new HttpsError(
            'unknown',
            'Failed to fetch group users.',
        );
    }
});

// Removed unused interface

const usersWhoLikedMe = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {currentUserId, page = 1, limit = 10} = data;

        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!currentUserId) {
            throw new HttpsError(
                'unauthenticated',
                'Must be called while authenticated.',
            );
        }

        try {
            const querySnapshot = await db
                .collectionGroup('likes')
                .where('userId', '==', currentUserId)
                .get();

            const userIds: string[] = [];
            querySnapshot.forEach((docSnap: any) => {
                const userDocRef: any | null = docSnap.ref.parent.parent;
                if (userDocRef && userDocRef.id !== currentUserId) {
                    userIds.push(userDocRef.id);
                }
            });

            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;

            return userIds.slice(startIndex, endIndex);
        } catch (error) {
            console.error('Error fetching users who liked me:', error);
            throw new HttpsError(
                'unknown',
                'Failed to fetch users who liked the current user.',
            );
        }
    },
);

// Removed unused interface

const dislikeProfile = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {userId, dislikedUserId} = data;
        const serverTime = FieldValue.serverTimestamp();
        
        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }
        
        if (!userId) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }
        if (!dislikedUserId) {
            throw new HttpsError(
                'invalid-argument',
                'The function must be called with a valid dislikedUserId.',
            );
        }

        try {
            const dislikeDocRef = await db
                .collection('users')
                .doc(userId)
                .collection('dislikes')
                .doc(dislikedUserId);

            await dislikeDocRef.set({
                id: dislikedUserId,
                timestamp: serverTime,
            });

            return {success: true};
        } catch (error) {
            console.error('Error disliking profile: ', error);
            throw new HttpsError(
                'unknown',
                'Failed to dislike profile.',
            );
        }
    },
);

// Removed unused interface

/**
 * Create or find a chat based on the current user (from context) and another user.
 * Returns a chatId that the client can use for messaging.
 */
const createOrFindChat = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {otherUserId, currentUserId} = data;
        const serverTime = FieldValue.serverTimestamp();
        if (!otherUserId) {
            throw new HttpsError(
                'invalid-argument',
                'otherUserId is required.',
            );
        }

        if (!currentUserId) {
            throw new HttpsError(
                'unauthenticated',
                'Must be authenticated to create a chat.',
            );
        }

        // Generate a reproducible ID for the chat: sort the two user IDs and join them
        const sortedParticipantIds = [currentUserId, otherUserId].sort();
        const chatDocId = sortedParticipantIds.join('_');

        // Reference to the chat doc
        const chatRef = db.collection('chats').doc(chatDocId);
        const chatSnap = await chatRef.get();

        // If chat doesn't exist, create it
        if (!chatSnap.exists) {
            // Store participant IDs or any metadata you need
            await chatRef.set({
                participants: sortedParticipantIds,
                createdAt: serverTime,
            });
        }

        // Return the chat ID so the client can store/retrieve messages
        return {chatId: chatDocId};
    },
);

// Removed unused interface

const sendMessage = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {chatId, text, currentUserId} = data;
        const serverTime = FieldValue.serverTimestamp();
        
        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }
        
        if (!chatId || !text) {
            throw new HttpsError(
                'invalid-argument',
                'chatId and text are required.',
            );
        }

        if (!currentUserId) {
            throw new HttpsError(
                'unauthenticated',
                'Must be signed in to send a message.',
            );
        }

        try {
            // Save message in "chats/{chatId}/messages"
            const messageRef = db
                .collection('chats')
                .doc(chatId)
                .collection('messages')
                .doc();
            await messageRef.set({
                text,
                senderId: currentUserId,
                timestamp: serverTime,
            });
            return {success: true};
        } catch (error) {
            console.error('Error sending message:', error);
            throw new HttpsError(
                'unknown',
                'Failed to send message.',
            );
        }
    },
);

const searchGroups = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
    const {data} = request;
    const { query } = data;
    if (!query) return { groups: [], hasMore: false, lastGroupId: null, lastGroupName: null };
    
    // Verify user is authenticated using v2 API
    if (!request.auth || !request.auth.uid) {
        throw new HttpsError(
            'unauthenticated',
            'The function must be called while authenticated.'
        );
    }

    const userId = request.auth.uid;
    const searchTerms = query.toLowerCase().split(/\s+/).filter((term: string) => term.length > 0);
    
    // Get user location for prioritization (optional)
    let userLocation = '';
    try {
        const userDoc = await db.collection('users').doc(userId).get();
        if (userDoc.exists) {
            userLocation = userDoc.data()?.location || '';
        }
    } catch (error) {
        console.log('Could not fetch user location for search prioritization');
    }
    
    const results = [];
    const localResults = [];
    let processedCount = 0;
    const maxProcessLimit = 200; // Limit processing to first 200 groups for efficiency
    
    // Use pagination to limit the groups we process
    const groupsSnapshot = await db.collection('Groups').limit(maxProcessLimit).get();
    
    for (const doc of groupsSnapshot.docs) {
        const groupData = doc.data();
        const searchTokens = groupData.searchTokens || [];
        const groupLocation = groupData.location || '';
        
        // Check if any search term partially matches any token
        const hasMatch = searchTerms.some((searchTerm: string) => 
            searchTokens.some((token: string) => 
                token.toLowerCase().includes(searchTerm) || searchTerm.includes(token.toLowerCase())
            )
        );
        
        if (hasMatch) {
            const groupResult = { 
                id: doc.id, 
                name: groupData.name, 
                location: groupLocation, 
                users: 1, 
                isMember: false 
            };
            
            // Prioritize local groups if user location is available
            if (userLocation && groupLocation && 
                groupLocation.toLowerCase().includes(userLocation.toLowerCase())) {
                localResults.push(groupResult);
            } else {
                results.push(groupResult);
            }
        }
        
        processedCount++;
    }
    
    // Combine results with local groups first
    const combinedResults = [...localResults, ...results];
    
    console.log('Search results found:', combinedResults.length, 'groups (', localResults.length, 'local,', results.length, 'other)');
    return { 
        groups: combinedResults.slice(0, 20), 
        hasMore: processedCount >= maxProcessLimit && combinedResults.length >= 20, 
        lastGroupId: null, 
        lastGroupName: null 
    };
});

// Removed unused interface

const leaveChat = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {chatId} = data;

        if (!chatId) {
            throw new HttpsError(
                'invalid-argument',
                'chatId is required.',
            );
        }

        const oldChat = await db.collection('chats').doc(chatId).get();

        if (oldChat.exists) {
            const oldChatData = await oldChat.data();

            const newChatRef = await db.collection('chats_done').doc(chatId);

            // Copy the old doc's data to the new doc
            await newChatRef.set(oldChatData);

            await db.collection('chats').doc(chatId).delete();
        }

        return 'success';
    },
);

// Removed unused interface

// Define the Vertex AI model details
const PROJECT_ID = '982232823164'; // Use the numeric project ID from error message
const LOCATION = 'us-central1'; // Choose the appropriate region
const MODEL_NAME = 'gemini-2.0-flash-001'; // Using Gemini Pro model

// Initialize Vertex AI
const vertexAI = new VertexAI({project: PROJECT_ID, location: LOCATION});
const generativeModel = vertexAI.preview.getGenerativeModel({
    model: MODEL_NAME,
});

// Replace the existing analyzeDescription function with this updated version
const analyzeDescription = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {description} = data;

        if (!description || description.trim().length === 0) {
            throw new HttpsError(
                'invalid-argument',
                'Description must not be empty.',
            );
        }

        try {
            // Use Vertex AI to extract keywords
            const keywords = await extractKeywordsWithVertexAI(description);

            return {
                success: true,
                keywords: keywords,
            };
        } catch (error) {
            console.error('Error analyzing description with Vertex AI:', error);

            // Fallback to simple extraction if Vertex AI fails
            try {
                const fallbackKeywords = extractKeywordsSimple(description);
                console.log('Using fallback keyword extraction method');

                return {
                    success: true,
                    keywords: fallbackKeywords,
                    usedFallback: true,
                };
            } catch (fallbackError) {
                console.error(
                    'Fallback keyword extraction also failed:',
                    fallbackError,
                );
                throw new HttpsError(
                    'unknown',
                    'Failed to analyze description.',
                );
            }
        }
    },
);

// Update the extractKeywordsWithVertexAI function to use the correct API
async function extractKeywordsWithVertexAI(text: string): Promise<string[]> {
    const prompt = `
Extract the most important keywords from the following text. 
These keywords should represent interests, hobbies, personality traits, or other important attributes.
Return ONLY the keywords as a comma-separated list, with no additional text, numbering, or explanation.
Choose 5-10 of the most significant keywords that would be useful for matching this person with groups.

TEXT: ${text}
  `;

    try {
        const request = {
            contents: [{role: 'user', parts: [{text: prompt}]}],
            generationConfig: {
                maxOutputTokens: 200,
                temperature: 0.2,
                topP: 0.8,
            },
        };

        const result = await generativeModel.generateContent(request);
        const response = await result.response;

        // Get the response text
        const responseText =
            response.candidates[0].content.parts[0].text.trim();

        // Split the comma-separated list and clean up each keyword
        const keywords = responseText
            .split(',')
            .map((word: string) => word.trim().toLowerCase())
            .filter((word: string) => word.length > 0);

        console.log('Keywords extracted with Vertex AI:', keywords);
        return keywords;
    } catch (error) {
        console.error('Error generating content with Vertex AI:', error);
        throw error;
    }
}

// Rename the old function for use as fallback
function extractKeywordsSimple(text: string): string[] {
    // Remove punctuation and convert to lowercase
    const cleanedText = text.toLowerCase().replace(/[^\w\s]/gi, '');

    // Split into words
    const words = cleanedText.split(/\s+/);

    // Filter out common stop words
    const stopWords = new Set([
        'a',
        'an',
        'the',
        'and',
        'or',
        'but',
        'is',
        'are',
        'was',
        'were',
        'i',
        'you',
        'he',
        'she',
        'it',
        'we',
        'they',
        'my',
        'your',
        'his',
        'her',
        'its',
        'our',
        'their',
        'this',
        'that',
        'these',
        'those',
        'am',
        'is',
        'are',
        'was',
        'were',
        'be',
        'been',
        'being',
        'have',
        'has',
        'had',
        'do',
        'does',
        'did',
        'will',
        'would',
        'shall',
        'should',
        'can',
        'could',
        'may',
        'might',
        'must',
        'to',
        'of',
        'in',
        'on',
        'at',
        'by',
        'for',
        'with',
        'about',
        'from',
        'like',
    ]);

    // Get keywords (non-stop words)
    const filteredWords = words.filter(
        word => word.length > 2 && !stopWords.has(word),
    );

    // Count word frequencies
    const wordFrequency: Record<string, number> = {};
    for (const word of filteredWords) {
        wordFrequency[word] = (wordFrequency[word] || 0) + 1;
    }

    // Sort by frequency and get top keywords
    const sortedWords = Object.entries(wordFrequency)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 15) // Get top 15 keywords
        .map(([word]) => word);

    return sortedWords;
}

const joinGroup = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const { groupId, selectedGroups } = data;

        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        const userId = request.auth.uid;

        if (!groupId) {
            throw new HttpsError(
                'invalid-argument',
                'The function must be called with a valid groupId.',
            );
        }

        try {
            // Add user to group's members
            const groupUserDoc = db
                .collection(`Groups/${groupId}/Group_Users`)
                .doc(userId);

            await groupUserDoc.set({
                joinedAt: FieldValue.serverTimestamp(),
                isCreator: false,
                id: userId,
            });

            // If selectedGroups array is provided, save it to the user's profile
            if (selectedGroups) {
                const userDoc = db.collection('users').doc(userId);
                await userDoc.set({groups: selectedGroups}, {merge: true});
            } else {
                // If not in bulk mode, just add this single group to user's groups array
                const userDoc = db.collection('users').doc(userId);
                await userDoc.update({
                    groups: FieldValue.arrayUnion(groupId),
                });
            }

            return {success: true};
        } catch (error) {
            console.error('Error joining group: ', error);
            throw new HttpsError(
                'unknown',
                'Failed to join the group.',
            );
        }
    },
);

// Removed unused interface

const leaveGroup = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {userId, groupId} = data;

        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!userId) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!groupId) {
            throw new HttpsError(
                'invalid-argument',
                'The function must be called with a valid groupId.',
            );
        }

        try {
            const batch = db.batch();

            // 1. Remove user from the Group_Users collection
            const groupUserRef = db
                .collection(`Groups/${groupId}/Group_Users`)
                .doc(userId);
            batch.delete(groupUserRef);

            // 2. Remove the group from the user's groups array
            const userRef = db.collection('users').doc(userId);
            batch.update(userRef, {
                groups: FieldValue.arrayRemove(groupId),
            });

            await batch.commit();

            // 3. Check if the group is now empty, and if so, delete it
            const groupUsersRef = db.collection(
                `Groups/${groupId}/Group_Users`,
            );
            const remainingUsers = await groupUsersRef.count().get();

            if (remainingUsers.data().count === 0) {
                await db.collection('Groups').doc(groupId).delete();
            }

            return {success: true};
        } catch (error) {
            console.error('Error leaving group: ', error);
            throw new HttpsError(
                'unknown',
                'Failed to leave the group.',
            );
        }
    },
);

// Removed unused interface

const updateFcmToken = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {userId, fcmToken} = data;

        if (!userId || !fcmToken) {
            throw new HttpsError(
                'invalid-argument',
                'userId and fcmToken are required.',
            );
        }

        try {
            // Update the user document with the new FCM token
            await db.collection('users').doc(userId).update({
                fcmToken: fcmToken,
                fcmTokenUpdatedAt: FieldValue.serverTimestamp(),
            });

            return {success: true};
        } catch (error) {
            console.error('Error updating FCM token:', error);
            throw new HttpsError(
                'unknown',
                'Failed to update FCM token.',
            );
        }
    },
);

// Removed unused interface

const checkUserProfile = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {userId} = data;

        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!userId) {
            throw new HttpsError(
                'invalid-argument',
                'userId is required.',
            );
        }

        try {
            const userDoc = await db.collection('users').doc(userId).get();
            
            if (!userDoc.exists) {
                return {
                    needsOnboarding: true,
                    hasImages: false,
                    reason: 'User document does not exist'
                };
            }

            const userData = userDoc.data();
            const hasImages = userData && userData.images && userData.images.length > 0;
            
            return {
                needsOnboarding: !hasImages,
                hasImages: hasImages,
                userData: hasImages ? {
                    name: userData.name,
                    images: userData.images,
                    groups: userData.groups || []
                } : null
            };
        } catch (error) {
            console.error('Error checking user profile:', error);
            throw new HttpsError(
                'unknown',
                'Failed to check user profile.',
            );
        }
    },
);

// Removed unused interface

const setupNewUser = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {userId, displayName} = data;

        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!userId) {
            throw new HttpsError(
                'invalid-argument',
                'userId is required.',
            );
        }

        try {
            const userDoc = db.collection('users').doc(userId);
            
            // Check if user already exists and has images
            const existingUser = await userDoc.get();
            if (existingUser.exists) {
                const userData = existingUser.data();
                const hasImages = userData && userData.images && userData.images.length > 0;
                
                return {
                    needsOnboarding: !hasImages,
                    existingUser: true,
                    userData: hasImages ? userData : null
                };
            }

            // Create new user document
            await userDoc.set({
                name: displayName || '',
                description: '',
                location: '',
                birthDate: '',
                images: [],
                terms_privacy_accepted: true,
                groups: [],
                createdAt: FieldValue.serverTimestamp(),
                lastActiveAt: FieldValue.serverTimestamp(),
            }, {merge: true});

            return {
                needsOnboarding: true,
                existingUser: false,
                userData: null
            };
        } catch (error) {
            console.error('Error setting up new user:', error);
            throw new HttpsError(
                'unknown',
                'Failed to setup new user.',
            );
        }
    },
);

// Removed unused interface

const createGroup = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        const {userId, groupName, location} = data;

        // Verify user is authenticated using v2 API
        if (!request.auth || !request.auth.uid) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!userId) {
            throw new HttpsError(
                'unauthenticated',
                'The function must be called while authenticated.',
            );
        }

        if (!groupName || groupName.trim().length === 0) {
            throw new HttpsError(
                'invalid-argument',
                'Group name is required.',
            );
        }

        try {
            const serverTime = FieldValue.serverTimestamp();
            
            // Create the group document WITH SEARCH TOKENS
            const groupRef = db.collection('Groups').doc();
            
            // Generate search tokens for the new group
            const name = groupName.trim();
            const words = name.toLowerCase().split(/\s+/).filter((word: string) => word.length > 2);
            const searchTokens = [name.toLowerCase(), ...words];
            
            await groupRef.set({
                name: name,
                description: '',
                createdAt: serverTime,
                updatedAt: serverTime,
                location: location || '',
                locationId: '', // Can be set later if needed
                isPublic: true,
                createdBy: userId,
                searchTokens: searchTokens,
            });

            // Add the creator as the first member
            const groupUserDoc = db
                .collection(`Groups/${groupRef.id}/Group_Users`)
                .doc(userId);

            await groupUserDoc.set({
                joinedAt: serverTime,
                isCreator: true,
                id: userId,
            });

            // Add the group to the user's groups array (create user doc if it doesn't exist)
            const userDoc = db.collection('users').doc(userId);
            const userSnapshot = await userDoc.get();
            
            if (userSnapshot.exists) {
                await userDoc.update({
                    groups: FieldValue.arrayUnion(groupRef.id),
                });
            } else {
                // Create user document if it doesn't exist
                await userDoc.set({
                    groups: [groupRef.id],
                    createdAt: serverTime,
                }, { merge: true });
                console.log(`Created missing user document for userId: ${userId}`);
            }

            console.log(`Created new group '${groupName}' with ID: ${groupRef.id} by user: ${userId}`);

            return {
                success: true,
                groupId: groupRef.id,
                groupName: groupName.trim(),
                message: `Successfully created group "${groupName.trim()}"`,
            };
        } catch (error) {
            console.error('Error creating group:', error);
            throw new HttpsError(
                'unknown',
                'Failed to create the group.',
            );
        }
    },
);

/**
 * SIMPLE MIGRATION FUNCTION
 * Run this once to add basic searchTokens to existing groups
 */
const migrateGroupsSearchTokens = onCall(
    {
        enforceAppCheck: true, // App Check enabled for security
    },
    async (request: any) => {
        const {data} = request;
        // Simple admin authentication
        if (data.adminKey !== 'migration_2025_search_tokens') {
            throw new HttpsError(
                'permission-denied',
                'Unauthorized migration attempt.',
            );
        }

        try {
            console.log('Starting migration: Adding basic search tokens to existing groups');
            
            const groupsSnapshot = await db.collection('Groups').get();
            const batch = db.batch();
            let processedCount = 0;
            let batchCount = 0;

            for (const doc of groupsSnapshot.docs) {
                const groupData = doc.data();
                
                // Skip if searchTokens already exist
                if (groupData.searchTokens && Array.isArray(groupData.searchTokens)) {
                    continue;
                }

                // Simple tokenization: just split the name into words
                const name = groupData.name || '';
                const words = name.toLowerCase().split(/\s+/).filter((word: string) => word.length > 2);
                const searchTokens = [name.toLowerCase(), ...words];

                batch.update(doc.ref, { searchTokens });
                
                processedCount++;
                batchCount++;

                // Commit every 400 updates (Firestore batch limit is 500)
                if (batchCount >= 400) {
                    await batch.commit();
                    console.log(`Migrated ${processedCount} groups so far...`);
                    batchCount = 0;
                }
            }

            // Commit remaining updates
            if (batchCount > 0) {
                await batch.commit();
            }

            console.log(`Migration completed: Updated ${processedCount} groups with search tokens`);

            return {
                success: true,
                processedCount,
                message: `Successfully migrated ${processedCount} groups with search tokens`,
            };

        } catch (error) {
            console.error('Migration error:', error);
            throw new HttpsError(
                'internal',
                'Migration failed.',
            );
        }
    },
);

// Add the exports at the end of the file if it's missing
module.exports = {
    getGroups,
    yourGroups,
    getGroup,
    likeProfile,
    dislikeProfile,
    usersWhoLikedMe,
    createOrFindChat,
    sendMessage,
    searchGroups,
    leaveChat,
    analyzeDescription,
    joinGroup,
    leaveGroup,
    updateFcmToken,
    checkUserProfile,
    setupNewUser,
    createGroup,
    migrateGroupsSearchTokens,
};
