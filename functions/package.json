{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "concurrently \"npm run build:watch\" \"firebase emulators:start\"", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@google-cloud/firestore": "^7.11.3", "@google-cloud/vertexai": "^1.9.3", "firebase-admin": "^13.4.0", "firebase-functions": "^6.4.0", "long": "^5.2.3"}, "devDependencies": {"concurrently": "^9.1.2", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}