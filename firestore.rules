rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read any user document when authenticated, and write their own
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
      
      // Allow users to read/write their own subcollections (likes, dislikes, etc.)
      match /{subcollection=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
        
    match /Groups/{groupId} {
      allow read, create, update, delete: if request.auth != null;

      match /Group_Users/{userId} {
        allow read: if request.auth != null;
        allow create, delete: if request.auth != null && request.auth.uid == userId;
        // Update is not allowed to prevent editing other users' group membership data
      }
    }

    // Allow authenticated users to read Locations
    match /Locations/{locationId} {
      allow read: if request.auth != null;
    }
    
    // Allow authenticated users to access chats
    match /chats/{chatId} {
      allow read, write: if request.auth != null;
      
      // Allow access to messages subcollection
      match /messages/{messageId} {
        allow read, write: if request.auth != null;
      }
    }
  }
}