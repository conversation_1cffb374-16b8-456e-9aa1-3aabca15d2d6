# Deploying the Vertex AI Keyword Extraction

This guide explains how to deploy the Vertex AI integration for keyword extraction in the Sootro app.

## Overview

We've enhanced the app's keyword extraction functionality by integrating with Google's Vertex AI, which provides more accurate and meaningful keywords from user descriptions. This implementation:

1. Uses the Gemini Pro model for advanced natural language understanding
2. Provides a fallback mechanism to the original simple extraction method if Vertex AI fails
3. Adds a visual "AI-powered" indicator to show when Vertex AI was used

## Prerequisites

1. Make sure the Google Cloud project has Vertex AI API enabled
2. Ensure the Firebase service account has access to Vertex AI
3. Firebase CLI installed and configured

## Deployment Steps

### 1. Enable the Required APIs in Google Cloud Console

Before deploying the functions, you must enable the required APIs:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to "APIs & Services" > "Dashboard"
4. Click on "+ ENABLE APIS AND SERVICES" at the top
5. Search for and enable the following APIs:
   - **Vertex AI API**
   - **Generative Language API** (for Gemini models)
   - **Cloud Storage API** (if not already enabled)

### 2. Configure Project Permissions

The Firebase service account needs proper permissions:

1. Go to "IAM & Admin" > "IAM"
2. Find the Firebase service account (usually named something like `<EMAIL>`)
3. Add the following roles:
   - **Vertex AI User**
   - **Vertex AI Service Agent**
   - **Cloud Storage Admin** (if you're uploading/processing data)

### 3. Update Project ID in Code

Make sure you're using the correct project ID in the `functions/src/index.ts` file:

```typescript
// Define the Vertex AI model details
const PROJECT_ID = '************'; // Your numeric project ID
const LOCATION = 'us-central1';    // Model region
const MODEL_NAME = 'gemini-pro';   // Model name
```

### 4. Install Dependencies

Navigate to the functions directory and install the required dependencies:

```bash
cd functions
npm install @google-cloud/vertexai@^1.9.3
```

### 5. Build & Deploy Functions

```bash
cd functions
npm run build
firebase deploy --only functions
```

### 6. Verify Deployment

After deployment:

1. Check Firebase console for any function deployment errors
2. Test the keyword extraction in the app by filling out a description in the onboarding flow
3. Verify the "AI-powered" badge appears when Vertex AI was used

## Troubleshooting

### Permission Issues

If you see errors like:
```
GoogleApiError: Project `************` is not allowed to use Publisher Model `projects/sootro-18482/locations/us-central1/publishers/google/models/gemini-pro`
```

Try these solutions:

1. **Enable the Gemini API (Generative Language API)**:
   - Go to Google Cloud Console > APIs & Services > Library
   - Search for "Generative Language API" and enable it

2. **Check Billing**:
   - Ensure the project has billing enabled
   - Go to Billing in Google Cloud Console and verify the project is linked to a billing account

3. **Verify Model Availability**:
   - Some regions might not have all models available
   - Try a different model or region if necessary

4. **Quota Limits**:
   - Check if you've hit API quota limits
   - Request quota increases if needed

### Fallback Implementation

If you continue facing issues with Vertex AI:

1. The system will automatically fall back to the simple keyword extraction method
2. Check your logs for errors to identify issues with the Vertex AI implementation

## Cost Considerations

Using Vertex AI with the Gemini model incurs costs based on the number of tokens processed. Monitor your usage in the Google Cloud Console and consider implementing rate limiting if needed.

## Testing the Integration

After deployment, you can test the functionality by:

1. Going through the onboarding flow and entering a detailed description
2. Checking Firebase function logs to see if Vertex AI was used or if it fell back to simple extraction
3. Verifying keywords are correctly extracted and match the description
