import { getReactNativePersistence } from '@firebase/auth/dist/rn/index.js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { initializeAuth } from 'firebase/auth';
import {
  connectFunctionsEmulator,
  getFunctions,
} from 'firebase/functions';
import {
  getFirestore,
  connectFirestoreEmulator
} from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { Platform } from 'react-native';
import appCheck from '@react-native-firebase/app-check';

// Platform-specific Firebase configuration
const getFirebaseConfig = () => {
  const baseConfig = {
    apiKey: 'AIzaSyD4yXlCs0hiZ_ILY0LNGxw8Rggu7XQt-3o',
    authDomain: 'sootro-18482.firebaseapp.com',
    projectId: 'sootro-18482',
    storageBucket: 'sootro-18482.firebasestorage.app',
    messagingSenderId: '982232823164',
  };

  // Use platform-specific App IDs to match native configurations
  if (Platform.OS === 'ios') {
    return {
      ...baseConfig,
      appId: '1:982232823164:ios:9854b9f606fe0df894a0f9', // iOS App ID
    };
  } else if (Platform.OS === 'android') {
    return {
      ...baseConfig,
      appId: '1:982232823164:android:5dd7f06c48906c6694a0f9', // Android App ID
    };
  } else {
    // Web fallback
    return {
      ...baseConfig,
      appId: '1:982232823164:web:6b652261671fcda694a0f9', // Web App ID
    };
  }
};

const firebaseConfig = getFirebaseConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage),
});

// App Check Debug Tokens - Replace these with your actual debug tokens from Firebase Console
const APP_CHECK_DEBUG_TOKENS = {
  ios: 'D65AAB80-B0CE-47E1-AFA1-D8C6D009D69D', // Replace with your iOS debug token
  android: 'DB439970-F799-4EFA-B695-4E00B3FCA154', // Replace with your Android debug token
};

// Initialize App Check with React Native Firebase
const initializeAppCheckForDev = async () => {
  try {
    if (__DEV__) {
      // In development mode, use debug provider with platform-specific tokens
      let debugToken;

      if (Platform.OS === 'ios') {
        debugToken = APP_CHECK_DEBUG_TOKENS.ios;
        console.log('🍎 Initializing App Check for iOS with debug token');
      } else if (Platform.OS === 'android') {
        debugToken = APP_CHECK_DEBUG_TOKENS.android;
        console.log('🤖 Initializing App Check for Android with debug token');
      } else {
        console.log('⚠️ App Check not configured for web platform');
        return null;
      }

      // Configure App Check with debug provider
      await appCheck().initializeAppCheck({
        provider: 'debug',
        debugToken: debugToken,
        isTokenAutoRefreshEnabled: true,
      });

      console.log(`✅ App Check initialized successfully for ${Platform.OS} with debug provider`);
      return appCheck();
    } else {
      // In production mode, use platform-specific providers
      let provider;

      if (Platform.OS === 'ios') {
        provider = 'appAttestWithDeviceCheckFallback';
        console.log('🍎 Initializing App Check for iOS with App Attest');
      } else if (Platform.OS === 'android') {
        provider = 'playIntegrity';
        console.log('🤖 Initializing App Check for Android with Play Integrity');
      } else {
        console.log('⚠️ App Check not configured for web platform in production');
        return null;
      }

      await appCheck().initializeAppCheck({
        provider: provider,
        isTokenAutoRefreshEnabled: true,
      });

      console.log(`✅ App Check initialized successfully for ${Platform.OS} with ${provider}`);
      return appCheck();
    }
  } catch (error) {
    console.error('❌ App Check initialization failed:', error);
    console.error('App will continue without App Check protection');
    return null;
  }
};

// Initialize App Check immediately
let appCheckInstance = null;
initializeAppCheckForDev().then((instance) => {
  appCheckInstance = instance;
}).catch((error) => {
  console.error('Failed to initialize App Check:', error);
});

const storage = getStorage(app);
const db = getFirestore(app);

const functions = getFunctions(app);

// Disable functions emulator - use production functions
// if (__DEV__) {
//   connectFunctionsEmulator(
//     functions,
//     process.env.EXPO_PUBLIC_LOCAL_FUNCTIONS_IP || 'localhost',
//     5001,
//   );
// }

// Function to get App Check token
const getAppCheckToken = async () => {
  try {
    if (appCheckInstance) {
      const { token } = await appCheckInstance.getToken();
      console.log('📱 App Check token retrieved successfully');
      return token;
    } else {
      console.warn('⚠️ App Check not initialized, cannot get token');
      return null;
    }
  } catch (error) {
    console.error('❌ Failed to get App Check token:', error);
    return null;
  }
};

// Function to check if App Check is initialized and working
const isAppCheckReady = () => {
  return appCheckInstance !== null;
};

// Function to get App Check instance
const getAppCheckInstance = () => {
  return appCheckInstance;
};

export {
  app,
  auth,
  functions,
  storage,
  db,
  getAppCheckToken,
  isAppCheckReady,
  getAppCheckInstance,
  initializeAppCheckForDev
};
