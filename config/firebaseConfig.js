import { getReactNativePersistence } from '@firebase/auth/dist/rn/index.js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { initializeAuth } from 'firebase/auth';
import {
  connectFunctionsEmulator,
  getFunctions,
} from 'firebase/functions';
import { 
  getFirestore, 
  connectFirestoreEmulator 
} from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { ReactNativeFirebaseAppCheckProvider } from '@react-native-firebase/app-check';
import { getApp } from '@react-native-firebase/app';
import { initializeAppCheck } from '@react-native-firebase/app-check';
import { Platform } from 'react-native';

// Platform-specific Firebase configuration
const getFirebaseConfig = () => {
  const baseConfig = {
    apiKey: 'AIzaSyD4yXlCs0hiZ_ILY0LNGxw8Rggu7XQt-3o',
    authDomain: 'sootro-18482.firebaseapp.com',
    projectId: 'sootro-18482',
    storageBucket: 'sootro-18482.firebasestorage.app',
    messagingSenderId: '982232823164',
  };

  // Use platform-specific App IDs to match native configurations
  if (Platform.OS === 'ios') {
    return {
      ...baseConfig,
      appId: '1:982232823164:ios:9854b9f606fe0df894a0f9', // iOS App ID
    };
  } else if (Platform.OS === 'android') {
    return {
      ...baseConfig,
      appId: '1:982232823164:android:5dd7f06c48906c6694a0f9', // Android App ID
    };
  } else {
    // Web fallback
    return {
      ...baseConfig,
      appId: '1:982232823164:web:6b652261671fcda694a0f9', // Web App ID
    };
  }
};

const firebaseConfig = getFirebaseConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage),
});

// Initialize App Check with new provider approach
const initializeAppCheckWithProvider = async () => {
  try {
    const rnfbProvider = new ReactNativeFirebaseAppCheckProvider();
    rnfbProvider.configure({
      android: {
        provider: __DEV__ ? 'debug' : 'playIntegrity',
        debugToken: 'DB439970-F799-4EFA-B695-4E00B3FCA154', // Debug token for development
      },
      apple: {
        provider: __DEV__ ? 'debug' : 'appAttestWithDeviceCheckFallback',
        debugToken: 'D65AAB80-B0CE-47E1-AFA1-D8C6D009D69D', // Debug token for development
      },
      web: {
        provider: 'reCaptchaV3',
        siteKey: 'your_site_key_here',
      },
    });

    const appCheck = await initializeAppCheck(getApp(), {
      provider: rnfbProvider,
      isTokenAutoRefreshEnabled: true,
    });

    console.log('App Check initialized successfully with provider');
    return appCheck;
  } catch (error) {
    console.error('App Check initialization failed:', error);
    // Continue without App Check if initialization fails
    return null;
  }
};

// Initialize App Check
initializeAppCheckWithProvider();

const storage = getStorage(app);
const db = getFirestore(app);

const functions = getFunctions(app);

// Disable functions emulator - use production functions
// if (__DEV__) {
//   connectFunctionsEmulator(
//     functions,
//     process.env.EXPO_PUBLIC_LOCAL_FUNCTIONS_IP || 'localhost',
//     5001,
//   );
// }

// Function to get App Check token (updated for new approach)
const getAppCheckToken = async () => {
  try {
    const appCheck = await initializeAppCheckWithProvider();
    if (appCheck) {
      const { token } = await appCheck.getToken();
      return token;
    }
    return null;
  } catch (error) {
    console.error('Failed to get App Check token:', error);
    return null;
  }
};

export { app, auth, functions, storage, db, getAppCheckToken };
