import { getReactNativePersistence } from '@firebase/auth/dist/rn/index.js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { initializeAuth } from 'firebase/auth';
import {
  connectFunctionsEmulator,
  getFunctions,
} from 'firebase/functions';
import {
  getFirestore,
  connectFirestoreEmulator
} from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { Platform } from 'react-native';
import { ReactNativeFirebaseAppCheckProvider } from '@react-native-firebase/app-check';
import { getApp } from '@react-native-firebase/app';
import { initializeAppCheck } from '@react-native-firebase/app-check';

// Platform-specific Firebase configuration
const getFirebaseConfig = () => {
  const baseConfig = {
    apiKey: 'AIzaSyD4yXlCs0hiZ_ILY0LNGxw8Rggu7XQt-3o',
    authDomain: 'sootro-18482.firebaseapp.com',
    projectId: 'sootro-18482',
    storageBucket: 'sootro-18482.firebasestorage.app',
    messagingSenderId: '982232823164',
  };

  // Use platform-specific App IDs to match native configurations
  if (Platform.OS === 'ios') {
    return {
      ...baseConfig,
      appId: '1:982232823164:ios:9854b9f606fe0df894a0f9', // iOS App ID
    };
  } else if (Platform.OS === 'android') {
    return {
      ...baseConfig,
      appId: '1:982232823164:android:5dd7f06c48906c6694a0f9', // Android App ID
    };
  } else {
    // Web fallback
    return {
      ...baseConfig,
      appId: '1:982232823164:web:6b652261671fcda694a0f9', // Web App ID
    };
  }
};

const firebaseConfig = getFirebaseConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth with persistence
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage),
});

// App Check Debug Tokens - Replace these with your actual debug tokens from Firebase Console
const APP_CHECK_DEBUG_TOKENS = {
  ios: '377E363A-20C4-4080-88A6-130B0554A217', // iOS Debug Token for CI, updated by Jonah Aug 23 2025
  android: '30B42B8B-93FF-401B-B061-FCBE5B2BFB29', // Android Debug Token for CI, updated by Jonah Aug 23 2025
};

// Initialize App Check with React Native Firebase using the correct API
const initializeAppCheckForDev = async () => {
  try {
    console.log(`🔐 Initializing App Check for ${Platform.OS} platform...`);

    // Step 1: Create and configure the custom provider
    const rnfbProvider = new ReactNativeFirebaseAppCheckProvider();

    if (__DEV__) {
      // Development mode configuration
      if (Platform.OS === 'ios') {
        console.log('🍎 Configuring App Check for iOS with debug token');
      } else if (Platform.OS === 'android') {
        console.log('🤖 Configuring App Check for Android with debug token');
      } else {
        console.log('⚠️ App Check not configured for web platform');
        return null;
      }

      rnfbProvider.configure({
        android: {
          provider: 'debug',
          debugToken: APP_CHECK_DEBUG_TOKENS.android,
        },
        apple: {
          provider: 'debug',
          debugToken: APP_CHECK_DEBUG_TOKENS.ios,
        },
        web: {
          provider: 'reCaptchaV3',
          siteKey: 'unknown',
        },
      });
    } else {
      // Production mode configuration
      console.log('🏭 Configuring App Check for production');

      rnfbProvider.configure({
        android: {
          provider: 'playIntegrity',
        },
        apple: {
          provider: 'appAttestWithDeviceCheckFallback',
        },
        web: {
          provider: 'reCaptchaV3',
          siteKey: 'unknown',
        },
      });
    }

    // Step 2: Initialize App Check with the configured provider
    const appCheck = await initializeAppCheck(getApp(), {
      provider: rnfbProvider,
      isTokenAutoRefreshEnabled: true,
    });

    console.log(`✅ App Check initialized successfully for ${Platform.OS}`);
    return appCheck;
  } catch (error) {
    console.error('❌ App Check initialization failed:', error);
    console.error('App will continue without App Check protection');
    return null;
  }
};

// Initialize App Check immediately
let appCheckInstance = null;
initializeAppCheckForDev().then((instance) => {
  appCheckInstance = instance;
}).catch((error) => {
  console.error('Failed to initialize App Check:', error);
});

const storage = getStorage(app);
const db = getFirestore(app);

const functions = getFunctions(app);

// Disable functions emulator - use production functions
// if (__DEV__) {
//   connectFunctionsEmulator(
//     functions,
//     process.env.EXPO_PUBLIC_LOCAL_FUNCTIONS_IP || 'localhost',
//     5001,
//   );
// }

// Function to get App Check token
const getAppCheckToken = async () => {
  try {
    if (appCheckInstance) {
      const { token } = await appCheckInstance.getToken();
      console.log('📱 App Check token retrieved successfully');
      return token;
    } else {
      console.warn('⚠️ App Check not initialized, cannot get token');
      return null;
    }
  } catch (error) {
    console.error('❌ Failed to get App Check token:', error);
    return null;
  }
};

// Function to check if App Check is initialized and working
const isAppCheckReady = () => {
  return appCheckInstance !== null;
};

// Function to get App Check instance
const getAppCheckInstance = () => {
  return appCheckInstance;
};

export {
  app,
  auth,
  functions,
  storage,
  db,
  getAppCheckToken,
  isAppCheckReady,
  getAppCheckInstance,
  initializeAppCheckForDev
};
