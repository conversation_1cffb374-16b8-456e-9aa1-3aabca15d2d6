{"expo": {"name": "<PERSON><PERSON>", "slug": "<PERSON><PERSON>", "version": "1.0.5", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "sootro", "userInterfaceStyle": "automatic", "statusBar": {"style": "dark", "backgroundColor": "#ffffff"}, "splash": {"image": "./assets/images/splashIcon.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.alexgrande.sootro", "googleServicesFile": "./GoogleService-Info.plist", "config": {"usesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "splash": {"image": "./assets/images/splashIcon.png", "resizeMode": "cover", "backgroundColor": "#ffffff"}, "package": "com.alexgrande.sootro", "googleServicesFile": "./google-services.json", "permissions": ["INTERNET"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-secure-store", "@react-native-firebase/app", "@react-native-firebase/auth", "@react-native-firebase/app-check", ["expo-build-properties", {"ios": {"useFrameworks": "static", "hermes": false}, "android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}}], "expo-font", "expo-notifications"], "experiments": {"typedRoutes": true}, "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/a4a75356-1cf2-4b7f-ab49-4e98c68be462", "enabled": true, "fallbackToCacheTimeout": 0, "checkAutomatically": "ON_LOAD"}}}