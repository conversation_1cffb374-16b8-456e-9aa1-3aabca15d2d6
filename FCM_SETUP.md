# Firebase Cloud Messaging Setup Instructions

This guide provides step-by-step instructions for setting up Firebase Cloud Messaging (FCM) in your Sootro app.

## Prerequisites

1. Access to the Firebase Console for the project
2. Ability to modify the app's source code
3. Ability to rebuild the app

## Setup Steps

### 1. Get Your VAPID Key from Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (sootro-18482)
3. Navigate to Project Settings > Cloud Messaging
4. Under the "Web configuration" section, click "Generate Key Pair"
5. Copy the generated public key

### 2. Configure Your Environment

Create a file named `.env` in the project root with the following:
```
EXPO_PUBLIC_VAPID_KEY=<paste_your_vapid_key_here>
EXPO_PUBLIC_PROJECT_ID=sootro-18482
```

### 3. Deploy the Service Worker

Ensure the `firebase-messaging-sw.js` file is in the root directory of your web server. This handles background notifications.

### 4. Rebuild the App

Run the following commands:

```bash
# Install dependencies
npm install

# Clean rebuild for iOS
npx expo prebuild --clean
cd ios && pod install && cd ..

# Run the app
npm start
```

## Verification

1. Check the app logs to see if FCM token registration is successful
2. Send a test notification from Firebase console
3. Verify that the app can receive notifications in both foreground and background states

## Troubleshooting

If you encounter issues:

1. **Missing FCM Token**: Check if `initializeFirebaseMessaging()` in `utils/pushNotifications.ts` is being called and returning a token
2. **Service Worker not registered**: Check browser console for any errors related to the service worker
3. **Permission Issues**: Ensure notification permissions are granted in the browser/device

For more details, refer to:
- [FCM Documentation](https://firebase.google.com/docs/cloud-messaging/js/client)
- [PUSH_NOTIFICATION_SETUP_GUIDE.md](./PUSH_NOTIFICATION_SETUP_GUIDE.md)

## References

- [Firebase Cloud Messaging for JavaScript](https://firebase.google.com/docs/cloud-messaging/js/client)
- [Firebase Cloud Messaging for Web](https://firebase.google.com/docs/cloud-messaging/js/receive) 