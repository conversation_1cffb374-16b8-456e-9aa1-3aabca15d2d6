import {Slot} from 'expo-router';
import {SessionProvider} from '../ctx';
import '@/global.css';
import Toast, {ErrorToast, InfoToast, SuccessToast} from 'react-native-toast-message';
import { useEffect } from 'react';
import { configurePushNotifications } from '@/utils/pushNotifications';
import { shouldShowReview, requestReview } from '@/utils/appReview';

export default function Root() {
    // Configure push notifications at the root level
    useEffect(() => {
        try {
            configurePushNotifications();
        } catch (error) {
            console.error('Failed to configure push notifications at app root:', error);
        }
    }, []);

    // Check if we should trigger an app review randomly
    useEffect(() => {
        const randomlyCheckForReview = async () => {
            try {
                // Only show the review dialog with a 10% chance when opening the app
                const randomChance = Math.random() < 0.1;
                if (randomChance) {
                    const shouldShow = await shouldShowReview();
                    if (shouldShow) {
                        // Wait a bit to ensure app is fully loaded
                        setTimeout(() => {
                            requestReview();
                        }, 3000);
                    }
                }
            } catch (error) {
                console.error('Error checking for app review:', error);
            }
        };

        randomlyCheckForReview();
    }, []);

    return (
        <SessionProvider>
            <Slot />
            <Toast visibilityTime={3000} topOffset={91} position={'top'} config={{
                success: (props) => (
                    <SuccessToast
                        {...props}
                        text1Style={{ fontSize: 16 }}
                        text2Style={{ fontSize: 14 }}
                    />
                ),
                error: (props) => (
                    <ErrorToast
                        {...props}
                        text1Style={{ fontSize: 16 }}
                        text2Style={{ fontSize: 14 }}
                    />
                ),
                info: (props) => (
                    <InfoToast
                        {...props}
                        text1Style={{ fontSize: 16 }}
                        text2Style={{ fontSize: 14 }}
                    />
                )

            }}/>
        </SessionProvider>
    );
}
