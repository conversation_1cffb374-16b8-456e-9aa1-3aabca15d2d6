import React, { useState } from 'react';
import { router } from 'expo-router';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    Linking,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
    createUserWithEmailAndPassword,
    GoogleAuthProvider,
    sendEmailVerification,
    signInWithCredential,
} from 'firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { auth, functions, waitForAppCheckReady } from '@/config/firebaseConfig';
import { useSession } from '../ctx';
import TitleTagline from '@/components/TitleTagline';
import Toast from 'react-native-toast-message';
import Checkbox from '../components/Checkbox';
import { httpsCallable } from 'firebase/functions';

GoogleSignin.configure({
    webClientId: '982232823164-46u9kqsh1vcduu1lc1hs0tli6879vr04.apps.googleusercontent.com',
    iosClientId: '982232823164-sg1v7ronorbmnv9n5ghfc715fsimhrne.apps.googleusercontent.com',
    offlineAccess: true,
});

const GoogleLogo = () => (
    <Image
        source={require('../assets/images/google-logo.webp')}
        style={styles.googleLogo}
        resizeMode="contain"
    />
);

export default function SignUp() {
    const { signUp } = useSession();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [isSigningUp, setIsSigningUp] = useState(false);
    const [isGoogleSigningUp, setIsGoogleSigningUp] = useState(false);
    const [termsAccepted, setTermsAccepted] = useState(false);

    const showError = (message: string) => {
        setErrorMessage(message);
        setTimeout(() => setErrorMessage(''), 5000);
    };

    const handleSignUp = async () => {
        if (!email || !password) {
            showError('Please enter both email and password');
            return;
        }

        if (!termsAccepted) {
            showError('Please accept the Terms of Service and Privacy Policy');
            return;
        }

        setIsSigningUp(true);
        try {
            // Wait for App Check to be ready before authentication
            console.log('🔐 Ensuring App Check is ready for email sign-up...');
            await waitForAppCheckReady();

            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            await sendEmailVerification(user);
            signUp(user, user.refreshToken);

            // Setup user document via Firebase Function
            await setupUserAndRoute(user.uid, user.displayName);

            Toast.show({
                type: 'success',
                text1: 'You successfully signed up!',
                text2: 'Please check your email for verification link.',
            });

            router.replace('/verify-email');
        } catch (error: any) {
            let message = 'Error signing up. Please try again.';

            switch (error.code) {
                case 'auth/email-already-in-use':
                    message = 'This email is already in use. Try signing in instead.';
                    break;
                case 'auth/invalid-email':
                    message = 'Please enter a valid email address.';
                    break;
                case 'auth/weak-password':
                    message = 'Password is too weak. Use at least 6 characters.';
                    break;
            }

            showError(message);
        } finally {
            setIsSigningUp(false);
        }
    };

    const handleGoogleSignUp = async () => {
        if (!termsAccepted) {
            showError('Please accept the Terms of Service and Privacy Policy');
            return;
        }

        setIsGoogleSigningUp(true);
        try {
            // Wait for App Check to be ready before authentication
            console.log('🔐 Ensuring App Check is ready for Google sign-up...');
            await waitForAppCheckReady();

            await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
            await GoogleSignin.signOut(); // Clear previous state

            const signInResult = await GoogleSignin.signIn();
            const idToken = extractIdToken(signInResult);

            if (!idToken) {
                throw new Error('No ID token found in Google Sign-In response');
            }

            const googleCredential = GoogleAuthProvider.credential(idToken);
            const userCredential = await signInWithCredential(auth, googleCredential);
            const user = userCredential.user;

            signUp(user, user.refreshToken || '');

            // Setup user and route accordingly
            await setupUserAndRoute(user.uid, user.displayName, true);
        } catch (error: any) {
            console.error('Google Sign-Up error:', error);
            showError(`Google Sign-Up failed: ${error.message || 'Unknown error'}`);
        } finally {
            setIsGoogleSigningUp(false);
        }
    };

    const extractIdToken = (result: any): string | null => {
        // Try direct access first
        if (result?.idToken) return result.idToken;
        if (result?.user?.idToken) return result.user.idToken;

        // Recursive search for token-like properties
        const findToken = (obj: any): string | null => {
            if (!obj || typeof obj !== 'object') return null;

            const tokenProps = ['idToken', 'id_token', 'token', 'accessToken'];

            for (const prop of tokenProps) {
                if (obj[prop] && typeof obj[prop] === 'string' && obj[prop].length > 20) {
                    return obj[prop];
                }
            }

            for (const key in obj) {
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    const found = findToken(obj[key]);
                    if (found) return found;
                }
            }

            return null;
        };

        return findToken(result);
    };

    const setupUserAndRoute = async (userId: string, displayName?: string | null, isGoogleSignUp = false) => {
        try {
            const setupNewUserCall = httpsCallable(functions, 'setupNewUser');
            const result = await setupNewUserCall({
                userId,
                displayName: displayName || undefined,
                isGoogleSignUp
            });
            const setupData = result.data as any;

            if (setupData.needsOnboarding) {
                router.push({
                    pathname: '/onboarding',
                    params: {
                        userNameParam: displayName || '',
                    },
                });
            } else {
                router.replace('/');
            }
        } catch (error) {
            console.error('Error setting up user:', error);
            // Fallback to onboarding on error
            router.push({
                pathname: '/onboarding',
                params: {
                    userNameParam: displayName || '',
                },
            });
        }
    };

    return (
        <KeyboardAwareScrollView
            enableOnAndroid={true}
            enableAutomaticScroll={Platform.OS === 'ios'}
            keyboardShouldPersistTaps="handled"
            extraScrollHeight={20}
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            style={styles.container}>

            <TitleTagline />

            <View style={styles.promoContainer}>
                <Text style={styles.promoText}>
                    Next 2,000 users get a free account until May 1st, 2026
                    with a chance to win $200!
                </Text>
            </View>

            <View style={styles.formContainer}>
                <Text style={styles.formTitle}>Create Your Account</Text>

                <TextInput
                    style={styles.input}
                    placeholder="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    placeholderTextColor="#999"
                />

                <TextInput
                    style={styles.input}
                    placeholder="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    placeholderTextColor="#999"
                />

                <View style={styles.termsContainer}>
                    <Checkbox
                        value={termsAccepted}
                        onValueChange={setTermsAccepted}
                    />
                    <View style={styles.termsTextContainer}>
                        <Text style={styles.termsText}>
                            I agree to the{' '}
                            <Text
                                style={styles.termsLink}
                                onPress={() => Linking.openURL('https://sootro.com/terms.html')}>
                                Terms of Service
                            </Text>{' '}
                            &{' '}
                            <Text
                                style={styles.termsLink}
                                onPress={() => Linking.openURL('https://sootro.com/privacy.html')}>
                                Privacy Policy
                            </Text>
                        </Text>
                    </View>
                </View>

                <TouchableOpacity
                    style={styles.signUpButton}
                    onPress={handleSignUp}
                    disabled={isSigningUp}>
                    {isSigningUp ? (
                        <ActivityIndicator color="#fff" size="small" />
                    ) : (
                        <Text style={styles.signUpButtonText}>Sign Up</Text>
                    )}
                </TouchableOpacity>

                <View style={styles.orDivider}>
                    <View style={styles.dividerLine} />
                    <Text style={styles.orText}>OR</Text>
                    <View style={styles.dividerLine} />
                </View>

                <TouchableOpacity
                    style={styles.googleButton}
                    onPress={handleGoogleSignUp}
                    disabled={isGoogleSigningUp}>
                    {isGoogleSigningUp ? (
                        <ActivityIndicator color="#4285F4" size="small" />
                    ) : (
                        <>
                            <GoogleLogo />
                            <Text style={styles.googleButtonText}>
                                Continue with Google
                            </Text>
                        </>
                    )}
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.linkContainer}
                    onPress={() => router.push('/sign-in')}>
                    <Text style={styles.link}>
                        Already have an account?{' '}
                        <Text style={styles.linkBold}>Sign In</Text>
                    </Text>
                </TouchableOpacity>
            </View>

            {errorMessage ? (
                <View style={styles.flashMessage}>
                    <Text style={styles.flashText}>{errorMessage}</Text>
                </View>
            ) : null}
        </KeyboardAwareScrollView>
    );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
    },
    formContainer: {
        width: '100%',
        maxWidth: 400,
        alignItems: 'center',
        marginTop: 20,
    },
    formTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 24,
        color: '#333',
        textAlign: 'center',
    },
    input: {
        width: '100%',
        height: 50,
        borderColor: '#ddd',
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 16,
        paddingHorizontal: 16,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
        color: '#333',
    },
    signUpButton: {
        width: '100%',
        height: 50,
        backgroundColor: '#C09B72',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    signUpButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    orDivider: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 24,
    },
    dividerLine: {
        flex: 1,
        height: 1,
        backgroundColor: '#e0e0e0',
    },
    orText: {
        marginHorizontal: 16,
        color: '#999',
        fontSize: 14,
    },
    googleButton: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 16,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    googleLogo: {
        width: 24,
        height: 24,
        marginRight: 12,
    },
    googleButtonText: {
        color: '#757575',
        fontSize: 16,
        fontWeight: '500',
    },
    promoContainer: {
        marginTop: 32,
        backgroundColor: '#f5f9ff',
        padding: 16,
        borderRadius: 8,
        borderLeftWidth: 4,
        borderLeftColor: '#4a80f5',
        width: '100%',
    },
    promoText: {
        fontSize: 14,
        lineHeight: 20,
        color: '#555',
        textAlign: 'center',
    },
    linkContainer: {
        marginTop: 24,
        paddingVertical: 8,
    },
    link: {
        color: '#666',
        fontSize: 16,
    },
    linkBold: {
        color: '#C09B72',
        fontWeight: 'bold',
    },
    flashMessage: {
        position: 'absolute',
        bottom: 20,
        width: '90%',
        backgroundColor: '#333',
        padding: 16,
        borderRadius: 8,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5,
    },
    flashText: {
        color: 'white',
        fontSize: 14,
        textAlign: 'center',
    },
    termsContainer: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    termsTextContainer: {
        flex: 1,
        marginLeft: 8,
    },
    termsText: {
        fontSize: 12,
        color: '#555',
        lineHeight: 16,
    },
    termsLink: {
        fontSize: 12,
        color: '#C09B72',
        textDecorationLine: 'underline',
    },
});