import React, {useCallback, useState} from 'react';
import {
    ActivityIndicator,
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getAuth} from 'firebase/auth';
import {
    collection,
    doc,
    getDoc,
    getFirestore,
    limit,
    onSnapshot,
    orderBy,
    query,
    QueryDocumentSnapshot,
    startAfter,
    where,
} from 'firebase/firestore';
import Colors from '@/constants/Colors';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// Define navigation params type
type RootStackParamList = {
  Connection: {
    userId: string;
  };
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'Connection'>;

// Define a type for connection
interface Connection {
  id: string;
  name: string;
}

const ConnectionsScreen = () => {

    const navigation = useNavigation();
    const [connections, setConnections] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [lastVisible, setLastVisible] =
        useState<QueryDocumentSnapshot | null>(null);
    const [isMoreLoading, setIsMoreLoading] = useState(false);
    const [hasMoreChats, setHasMoreChats] = useState(true);
    const CHATS_PER_PAGE = 10;

    const loadChats = useCallback((lastDoc = null) => {
        const auth = getAuth();
        const currentUser = auth.currentUser;
        if (!currentUser) {
            setIsLoading(false);
            return;
        }

        const db = getFirestore();
        const chatsRef = collection(db, 'chats');

        let baseQuery = query(
            chatsRef,
            where('participants', 'array-contains', currentUser.uid),
            orderBy('createdAt', 'desc'),
            limit(CHATS_PER_PAGE),
        );

        const paginatedQuery = lastDoc
            ? query(baseQuery, startAfter(lastDoc))
            : baseQuery;

        // Subscribe to changes in the query
        const unsubscribe = onSnapshot(paginatedQuery, async snapshot => {
            // Store the last visible document for pagination
            const lastVisible =
                snapshot.docs.length > 0
                    ? snapshot.docs[snapshot.docs.length - 1]
                    : null;

            setLastVisible(lastVisible);
            setHasMoreChats(snapshot.docs.length === CHATS_PER_PAGE);

            const chatDocs = snapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data(),
            }));

            // Convert each chatDoc into a connection object
            const results = await Promise.all(
                chatDocs.map(async chat => {
                    const otherUserId = chat.participants.find(
                        (p: string) => p !== currentUser.uid,
                    );
                    if (!otherUserId) return null;

                    // Fetch display name from Firestore users collection
                    const userDocRef = doc(db, 'users', otherUserId);
                    const userSnap = await getDoc(userDocRef);

                    let name = 'Unknown';
                    if (userSnap.exists()) {
                        const userData = userSnap.data();
                        name = userData.name || 'Connection';
                    }

                    return {
                        id: otherUserId,
                        name,
                    };
                }),
            );

            // Filter out any nulls
            const newConnections = results.filter(Boolean);

            // If loading more, append to existing connections
            if (lastDoc) {
                setConnections(prev => [...prev, ...newConnections]);
            } else {
                setConnections(newConnections);
            }

            setIsLoading(false);
            setIsMoreLoading(false);
        });

        return unsubscribe;
    }, []);

    useFocusEffect(
        useCallback(() => {
            setIsLoading(true);
            setLastVisible(null);
            setConnections([]);

            const unsubscribe = loadChats();

            // Clean up on unmount or screen blur
            return () => unsubscribe && unsubscribe();
        }, [loadChats]),
    );


    const loadMoreChats = () => {
        if (!hasMoreChats || isMoreLoading) return;

        setIsMoreLoading(true);
        loadChats(lastVisible);
    };

    const renderFooter = () => {
        if (!isMoreLoading) return null;

        return (
            <View style={styles.footerLoader}>
                <ActivityIndicator size="small" />
            </View>
        );
    };

    const ConnectionItem = ({item}: any) => {
        return (
            <TouchableOpacity
                style={styles.item}
                onPress={() =>
                    navigation.navigate('Connection', {
                        userId: item.id,
                    })
                }>
                <Text style={styles.name}>{item.name}</Text>
                {item.lastMessage && (
                    <Text style={styles.lastMessage} numberOfLines={1}>
                        {item.lastMessage}
                    </Text>
                )}
            </TouchableOpacity>
        );
    };

    return (
        <View style={styles.container}>
            {isLoading ? (
                <View style={styles.loaderContainer}>
                    <ActivityIndicator size="large" />
                </View>
            ) : connections.length === 0 ? (
                <View style={styles.noConnectionWrapper}>
                    <Text style={styles.noConnectionText}>
                        No connections currently
                    </Text>
                </View>
            ) : (
                <FlatList
                    data={connections}
                    keyExtractor={item => item.id}
                    renderItem={({item}) => <ConnectionItem item={item} />}
                    onEndReached={loadMoreChats}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={renderFooter}
                />
            )}

        </View>
    );
};

const styles = StyleSheet.create({

  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 16,
  },
  item: {
    padding: 16,
    marginBottom: 10,
    backgroundColor: Colors.backgroundGray,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  name: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  noConnectionWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  noConnectionText: {
    fontSize: 18,
    color: Colors.textSecondary,
    textAlign: 'center',
    width: '80%',
  }
});

export default ConnectionsScreen;
