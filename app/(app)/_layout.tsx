import {Text} from 'react-native';
import {Redirect} from 'expo-router';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {createStackNavigator} from '@react-navigation/stack';
import HomeScreen from './index';
import GroupsScreen from './groups';
import GroupScreen from './group';
import GroupCreateScreen from './groupCreate';
import EditProfile from './profileEdit';
import Support from './support';
import ConnectionsScreen from './connections';
import ConnectionScreen from './connection';
import LikesScreen from './likes';
import Icon from 'react-native-vector-icons/FontAwesome';
import {useSession} from '../../ctx';
import {
    Menu,
    MenuOption,
    MenuOptions,
    MenuProvider,
    MenuTrigger,
} from 'react-native-popup-menu';
import '@/global.css';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

function HomeStack() {
    const {signOut} = useSession();

    return (
        <Stack.Navigator>
            <Stack.Screen
                name="Sootro"
                component={HomeScreen}
                options={({navigation}) => ({
                    headerRight: () => (
                        <Menu>
                            <MenuTrigger>
                                <Icon
                                    name="bars"
                                    size={25}
                                    style={{marginRight: 10}}
                                />
                            </MenuTrigger>
                            <MenuOptions>
                                <MenuOption
                                    onSelect={() =>
                                        navigation.navigate('Support')
                                    }>
                                    <Text style={{padding: 10}}>Support</Text>
                                </MenuOption>
                                <MenuOption
                                    onSelect={() => {
                                        signOut();
                                        navigation.navigate('sign-in');
                                    }}>
                                    <Text style={{padding: 10}}>Sign Out</Text>
                                </MenuOption>
                            </MenuOptions>
                        </Menu>
                    ),
                })}
            />
            <Stack.Screen
                name="EditProfile"
                component={EditProfile}
                options={{title: 'Edit Profile'}}
            />
            <Stack.Screen name="Support" component={Support} />
            <Stack.Screen name="Group" component={GroupScreen} />
        </Stack.Navigator>
    );
}

function GroupsStack() {
    return (
        <Stack.Navigator>
            <Stack.Screen name="Groups" component={GroupsScreen} />
            <Stack.Screen name="Group" component={GroupScreen} />
            <Stack.Screen name="GroupCreate" component={GroupCreateScreen} />
        </Stack.Navigator>
    );
}

function ConnectionsStack() {
    return (
        <Stack.Navigator>
            <Stack.Screen name="Connections" component={ConnectionsScreen} />
            <Stack.Screen name="Connection" component={ConnectionScreen} />
        </Stack.Navigator>
    );
}

function LikesStack() {
    return (
        <Stack.Navigator>
            <Stack.Screen name="Likes" component={LikesScreen} />
        </Stack.Navigator>
    );
}

function AppTabs() {
    return (
        <Tab.Navigator
            screenOptions={({route}) => ({
                tabBarIcon: ({color, size}) => {
                    let iconName;

                    if (route.name === 'Home') {
                        iconName = 'home';
                    } else if (route.name === 'Groups') {
                        iconName = 'users';
                    } else if (route.name === 'Connections') {
                        iconName = 'comments';
                    } else if (route.name === 'Likes') {
                        iconName = 'heart';
                    }

                    return (
                        <Icon name={iconName || ''} size={19} color={color} />
                    );
                },
                headerShown: false,
            })}>
            <Tab.Screen name="Home" component={HomeStack} />
            <Tab.Screen name="Groups" component={GroupsStack} />
            <Tab.Screen name="Connections" component={ConnectionsStack} />
            <Tab.Screen name="Likes" component={LikesStack} />
        </Tab.Navigator>
    );
}

export default function AppLayout() {
    const {session, isLoading} = useSession();

    if (isLoading) {
        return <Text>Loading...</Text>;
    }

    if (!session) {
        return <Redirect href="/sign-in" />;
    }

    return (
        <MenuProvider>
            <AppTabs />
        </MenuProvider>
    );
}
