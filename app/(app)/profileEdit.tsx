import {auth, storage} from '@/config/firebaseConfig';
import DateTimePicker from '@react-native-community/datetimepicker';
import {NavigationProp} from '@react-navigation/native';
import * as ImagePicker from 'expo-image-picker';
import {onAuthStateChanged, User} from 'firebase/auth';
import {doc, getDoc, getFirestore, setDoc} from 'firebase/firestore';
import {
    deleteObject,
    getDownloadURL,
    ref,
    uploadBytesResumable,
} from 'firebase/storage';
import React, {useEffect, useState} from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {uuid} from 'expo-modules-core';
import Toast from 'react-native-toast-message';
import Colors from '@/constants/Colors';
import { saveProfileImages } from '@/utils/imageUpload';

const EditProfileScreen = ({navigation}: {navigation: NavigationProp<any>}) => {
    // Existing remote images
    const [savedImages, setSavedImages] = useState<string[]>([]);
    // New images to be uploaded
    const [newImages, setNewImages] = useState<ImagePicker.ImagePickerAsset[]>(
        [],
    );
    // Images marked for deletion
    const [imagesToDelete, setImagesToDelete] = useState<string[]>([]);
    const [profileName, setProfileName] = useState('');
    const [description, setDescription] = useState('');
    const [location, setLocation] = useState('');
    const [birthDate, setBirthDate] = useState(new Date());
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [user, setUser] = useState<User | null>();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, user => {
            if (user) {
                const uid = user.uid;
                fetchProfileData(uid);
                setUser(user);
            } else {
                setUser(null);
            }
        });
        return () => unsubscribe();
    }, []);

    const fetchProfileData = async (userId: string) => {
        const db = getFirestore();
        const userDoc = doc(db, 'users', userId);
        const docSnap = await getDoc(userDoc);

        if (docSnap.exists()) {
            const data = docSnap.data();
            setProfileName(data.name || '');
            setDescription(data.description || '');
            setLocation(data.location || '');
            setBirthDate(new Date(data.birthDate || new Date()));
            setSavedImages(data.images || []);
        }
    };

    const handlePickImage = async () => {
        const remainingSlots =
            6 - (savedImages.length + newImages.length - imagesToDelete.length);

        if (remainingSlots <= 0) {
            Toast.show({
                type: 'info',
                text1: 'Maximum images reached',
                text2: 'You can only have up to 6 profile images.',
            });
            return;
        }

        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsMultipleSelection: true,
            selectionLimit: remainingSlots,
            quality: 0.8,
            aspect: [1, 1],
        });

        if (!result.canceled && result.assets) {
            setNewImages(prev => [...prev, ...result.assets]);
        }
    };

    const handleDeleteImage = (
        image: string | ImagePicker.ImagePickerAsset,
    ) => {
        const totalImages =
            savedImages.length + newImages.length - imagesToDelete.length;
        if (totalImages <= 1) {
            Toast.show({
                type: 'error',
                text1: 'Error deleting image',
                text2: "Can't delete the only image, please upload another image first",
            });
            return;
        }

        Alert.alert(
            'Delete Image',
            'Are you sure you want to delete this image?',
            [
                {text: 'Cancel', style: 'cancel'},
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => {
                        if (typeof image === 'string') {
                            setImagesToDelete(prev => [...prev, image]);
                        } else {
                            setNewImages(prev => prev.filter(img => img.uri !== image.uri));
                        }
                    },
                },
            ],
        );
    };

    const handleSaveProfile = async () => {
        if (
            savedImages.length + newImages.length - imagesToDelete.length === 0
        ) {
            Toast.show({
                type: 'error',
                text1: 'Error saving profile',
                text2: 'Please upload at least one profile image',
            });
            return;
        }
        
        if (!profileName.trim()) {
            Toast.show({
                type: 'error',
                text1: 'Error saving profile',
                text2: 'Please enter your profile name',
            });
            return;
        }
        
        if (!user) return;

        setLoading(true);

        try {
            const db = getFirestore();
            
            // Use the shared utility function for image upload
            const finalImages = await saveProfileImages({
                db,
                userId: user.uid,
                savedImages,
                newImages,
                imagesToDelete,
                setIsSubmitting: (isSubmitting) => setLoading(isSubmitting),
                additionalData: {
                    name: profileName,
                    description: description,
                    location: location,
                    birthDate: birthDate.toISOString(),
                }
            });

            if (finalImages) {
                // Update local state
                setSavedImages(finalImages);
                setNewImages([]);
                setImagesToDelete([]);
                
                Toast.show({
                    type: 'success',
                    text1: 'Success!',
                    text2: 'Profile saved successfully',
                });
                
                // Navigate back after saving
                setTimeout(() => {
                    navigation.goBack();
                }, 500);
            }
        } catch (error) {
            console.log('Profile save error:', error);
            Toast.show({
                type: 'error',
                text1: 'Error saving profile',
                text2: 'Please try again later',
            });
            setLoading(false);
        }
    };

    return (
        <View style={styles.flexContainer}>
            <KeyboardAwareScrollView 
                contentContainerStyle={styles.scrollContainer}
                enableOnAndroid={true}
                enableAutomaticScroll={Platform.OS === 'ios'}
                keyboardShouldPersistTaps="handled"
                extraScrollHeight={20}>
                <View style={styles.container}>
                    <Text style={styles.title}>Edit Profile</Text>
                    <TextInput
                        style={styles.input}
                        placeholder="Profile Name"
                        value={profileName}
                        onChangeText={setProfileName}
                    />
                    <View style={styles.imageGrid}>
                        {savedImages
                            .filter(img => !imagesToDelete.includes(img))
                            .map((image, index) => (
                                <TouchableOpacity
                                    key={`saved-${index}`}
                                    onPress={() => handleDeleteImage(image)}
                                    style={[
                                        styles.imageView,
                                        (index + 1) % 3 === 0
                                            ? styles.noMargin
                                            : null,
                                    ]}>
                                    <Image
                                        source={{uri: image}}
                                        style={styles.image}
                                    />
                                </TouchableOpacity>
                            ))}
                        {newImages.map((image, index) => (
                            <TouchableOpacity
                                key={`new-${index}`}
                                onPress={() => handleDeleteImage(image)}
                                style={[
                                    styles.imageView,
                                    (savedImages.length + index + 1) % 3 === 0
                                        ? styles.noMargin
                                        : null,
                                ]}>
                                <Image
                                    source={{uri: image.uri}}
                                    style={styles.image}
                                />
                            </TouchableOpacity>
                        ))}
                        {savedImages.length +
                            newImages.length -
                            imagesToDelete.length <
                            6 && (
                            <TouchableOpacity
                                onPress={handlePickImage}
                                style={[
                                    styles.addImage,
                                    (savedImages.length +
                                        newImages.length -
                                        imagesToDelete.length +
                                        1) %
                                        3 ===
                                    0
                                        ? styles.noMargin
                                        : null,
                                ]}>
                                <Text style={styles.addImageText}>+</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                    <TextInput
                        style={[styles.input, styles.textArea]}
                        placeholder="Description"
                        value={description}
                        onChangeText={setDescription}
                        multiline
                        textAlignVertical="top"
                    />
                    <TextInput
                        style={styles.input}
                        placeholder="Location"
                        value={location}
                        onChangeText={setLocation}
                    />
                    <View style={styles.datePickerContainer}>
                        {Platform.OS === 'android' && (
                            <>
                                <Text style={styles.dateText}>
                                    Birth Date: {birthDate.toLocaleDateString()}
                                </Text>
                                
                                <TouchableOpacity 
                                    style={styles.dateButton}
                                    onPress={() => setShowDatePicker(true)}>
                                    <Text style={styles.dateButtonText}>Change Date</Text>
                                </TouchableOpacity>
                            </>
                        )}
                        
                        {(showDatePicker || Platform.OS === 'ios') && (
                            <DateTimePicker
                                value={birthDate}
                                mode="date"
                                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                                onChange={(event, selectedDate) => {
                                    if (Platform.OS === 'android') {
                                        setShowDatePicker(false);
                                    }
                                    if (selectedDate) {
                                        setBirthDate(selectedDate);
                                    }
                                }}
                            />
                        )}
                    </View>
                    <View style={styles.loaderContainer}>
                        <TouchableOpacity
                            style={[
                                styles.saveButton, 
                                loading ? styles.saveButtonDisabled : {}
                            ]}
                            onPress={handleSaveProfile}
                            disabled={loading}>
                            {loading ? (
                                <View style={styles.loadingButtonContent}>
                                    <ActivityIndicator size="small" color="#FFFFFF" />
                                    <Text style={styles.saveButtonText}>Saving...</Text>
                                </View>
                            ) : (
                                <Text style={styles.saveButtonText}>Save Profile</Text>
                            )}
                        </TouchableOpacity>
                    </View>
                </View>
            </KeyboardAwareScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    flexContainer: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: '#ffffff',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
        color: '#333',
    },
    input: {
        width: '100%',
        height: 40,
        borderColor: '#ccc',
        borderWidth: 1,
        borderRadius: 5,
        marginBottom: 15,
        paddingHorizontal: 10,
        backgroundColor: '#f9f9f9',
    },
    textArea: {
        height: 120,
        paddingTop: 10,
        textAlignVertical: 'top',
    },
    datePickerContainer: {
        alignItems: 'flex-start',
        marginBottom: 20,
    },
    dateText: {
        fontSize: 16,
        marginBottom: 10,
        color: '#444',
    },
    dateButton: {
        paddingVertical: 8,
        paddingHorizontal: 12,
        backgroundColor: '#f0f0f0',
        borderRadius: 5,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    dateButtonText: {
        color: '#444',
    },
    imageGrid: {
        width: '100%',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        marginBottom: 15,
    },
    imageView: {
        width: '30%',
        aspectRatio: 1,
        marginRight: '5%',
        marginBottom: '3%',
        height: 'auto',
        borderRadius: 5,
        borderColor: '#eee',
        borderWidth: 1,
        overflow: 'hidden',
    },
    image: {
        resizeMode: 'cover',
        height: '100%',
        width: '100%',
    },
    noMargin: {
        marginRight: 0,
    },
    addImage: {
        width: '30%',
        aspectRatio: 1,
        height: 100,
        marginRight: '5%',
        marginBottom: '3%',
        borderRadius: 5,
        borderColor: '#ddd',
        borderWidth: 1,
        borderStyle: 'dashed',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f5f5f5',
    },
    addImageText: {
        fontSize: 28,
        color: '#888',
    },
    loaderContainer: {
        marginTop: 20,
        marginBottom: 20,
    },
    saveButton: {
        backgroundColor: '#C09B72',
        paddingVertical: 18,
        paddingHorizontal: 20,
        borderRadius: 12,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.15,
        shadowRadius: 5,
        elevation: 3,
    },
    saveButtonDisabled: {
        backgroundColor: '#E9E1D8',
        shadowOpacity: 0,
    },
    saveButtonText: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: 16,
    },
    loadingButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
});

export default EditProfileScreen;