import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { useNavigation } from 'expo-router';
import Colors from '@/constants/Colors';
import { requestReview, resetReviewData } from '@/utils/appReview';
import { getAuth, signOut } from 'firebase/auth';

const Settings = () => {
  const navigation = useNavigation();
  const auth = getAuth();

  React.useLayoutEffect(() => {
    navigation.setOptions({
      title: 'Settings'
    });
  }, [navigation]);

  const handleRequestReview = async () => {
    try {
      await requestReview();
      Alert.alert('Review Requested', 'App store review dialog has been requested.');
    } catch (error) {
      console.error('Error requesting review:', error);
      Alert.alert('Error', 'Failed to request app review.');
    }
  };

  const handleResetReviewData = async () => {
    try {
      await resetReviewData();
      Alert.alert('Reset Complete', 'App review data has been reset.');
    } catch (error) {
      console.error('Error resetting review data:', error);
      Alert.alert('Error', 'Failed to reset app review data.');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Error', 'Failed to sign out.');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Review</Text>
        <TouchableOpacity 
          style={styles.button}
          onPress={handleRequestReview}
        >
          <Text style={styles.buttonText}>Test App Store Review</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: Colors.warning }]}
          onPress={handleResetReviewData}
        >
          <Text style={styles.buttonText}>Reset Review Data</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account</Text>
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: Colors.error }]}
          onPress={handleSignOut}
        >
          <Text style={styles.buttonText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 16,
  },
  section: {
    marginBottom: 24,
    backgroundColor: Colors.card,
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: Colors.text,
  },
  button: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default Settings;
