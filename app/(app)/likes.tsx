import React, {useCallback, useState} from 'react';
import {
    ActivityIndicator,
    FlatList,
    StyleSheet,
    Text,
    View,
} from 'react-native';
import UserProfileCard from '@/components/UserProfileCard';
import {getFunctions, httpsCallable} from 'firebase/functions';
import {doc, getDoc, getFirestore} from 'firebase/firestore';
import {getAuth} from 'firebase/auth';
import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import Colors from '@/constants/Colors';

const Likes = () => {
    const auth = getAuth();
    const currentUser = auth.currentUser;
    const [users, setUsers] = useState([]);
    const isFocused = useIsFocused();
    const [isLoading, setIsLoading] = useState(true);

    const [page, setPage] = useState(1);
    const [limit] = useState(10); // Number of items per page
    const [hasMoreData, setHasMoreData] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    const fetchWhoLikedMe = async (pageToFetch = 1, shouldAppend = false) => {
        if (!currentUser) return;

        if (pageToFetch === 1) {
            setIsLoading(true);
        } else {
            setIsLoadingMore(true);
        }

        const functions = getFunctions();
        const usersWhoLikedMe = httpsCallable(functions, 'usersWhoLikedMe');

        try {
            const result: any = await usersWhoLikedMe({
                currentUserId: currentUser.uid,
                page: pageToFetch,
                limit: limit,
            });

            const userIds = result.data || [];

            if (userIds.length < limit) {
                setHasMoreData(false);
            }

            const db = getFirestore();
            const userDocs = await Promise.all(
                userIds.map(async (uid: string) => {
                    const userRef = doc(db, 'users', uid);
                    const userSnap = await getDoc(userRef);
                    if (userSnap.exists()) {
                        return {id: userSnap.id, ...userSnap.data()};
                    }
                    return null;
                }),
            );

            const filteredUsers = userDocs.filter(Boolean);

            if (shouldAppend) {
                setUsers(prevUsers => [...prevUsers, ...filteredUsers]);
            } else {
                setUsers(filteredUsers);
            }
        } catch (error) {
            console.error('Error fetching users who liked me:', error);
        } finally {
            setIsLoading(false);
            setIsLoadingMore(false);
        }
    };

    useFocusEffect(
        useCallback(() => {
            setPage(1);
            setHasMoreData(true);
            fetchWhoLikedMe(1, false);
        }, []),
    );

    const loadMoreData = () => {
        if (hasMoreData && !isLoadingMore) {
            const nextPage = page + 1;
            setPage(nextPage);
            fetchWhoLikedMe(nextPage, true);
        }
    };

    const handleLikeProfile = (id: string) => {
        setUsers(prevProfiles =>
            prevProfiles.filter(profile => profile.id !== id),
        );
    };

    const handleRemoveProfile = (id: string) => {
        setUsers(prevProfiles =>
            prevProfiles.filter(profile => profile.id !== id),
        );
    };

    const renderFooter = () => {
        if (!isLoadingMore) return null;

        return (
            <View style={styles.footerLoader}>
                <ActivityIndicator size="small" />
            </View>
        );
    };

    return (
        <View style={styles.container}>
            {isLoading ? (
                <ActivityIndicator style={{ marginTop: 20 }} color={Colors.primary} size="large" />
            ) : users.length === 0 ? (
                <View style={styles.noLikesWrapper}>
                    <Text style={styles.noLikesText}>You have no likes at the moment</Text>
                </View>
            ) : (
                <FlatList
                    data={users}
                    renderItem={({item}) => (
                        <UserProfileCard
                            profile={item}
                            onLike={handleLikeProfile}
                            onRemove={handleRemoveProfile}
                        />
                    )}
                    keyExtractor={item => item.id}
                    horizontal
                    pagingEnabled
                    showsHorizontalScrollIndicator={false}
                    onEndReached={loadMoreData}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={renderFooter}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    noLikesWrapper: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    footerLoader: {
        paddingHorizontal: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noLikesText: {
        fontSize: 18,
        color: Colors.textSecondary,
        textAlign: 'center',
        width: '80%',
    }
});

export default Likes;
