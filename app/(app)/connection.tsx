import React, { useEffect, useState, useRef } from 'react';
import { View, Text, FlatList, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { getAuth } from 'firebase/auth';
import { getFunctions, httpsCallable, Functions } from 'firebase/functions';
import { getFirestore, collection, onSnapshot, orderBy, query, doc, getDoc, serverTimestamp, Timestamp } from 'firebase/firestore';
import Colors from '@/constants/Colors';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

// Define a message interface
interface Message {
  id: string;
  text: string;
  senderId: string;
  timestamp: any;
  pending?: boolean;
}

// Add a type for route params
interface RouteParams {
  userId: string;
}

const MessageItem = ({ item, currentUserUid }: { item: Message; currentUserUid: string | undefined }) => {
  const isCurrentUser = item.senderId === currentUserUid;
  return (
    <View
      style={[
        styles.messageItem,
        isCurrentUser ? styles.yourMessage : styles.theirMessage,
        item.pending ? styles.pendingMessage : null,
      ]}
    >
      <Text 
        style={[
          styles.messageText, 
          isCurrentUser ? styles.yourMessageText : styles.theirMessageText,
          item.pending ? styles.pendingMessageText : null,
        ]}
      >
        {item.text}
      </Text>
    </View>
  );
};

const MessagingShowPage = () => {
  const flatListRef = useRef<FlatList<Message>>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [chatId, setChatId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [chatPartnerName, setChatPartnerName] = useState<string | null>(null);

  const navigation = useNavigation();
  const route = useRoute();
  const { userId } = (route.params as RouteParams) || { userId: '' };

  const auth = getAuth();
  const currentUser = auth.currentUser;

  useEffect(() => {
    const fetchUserName = async () => {
      const db = getFirestore();
      const userDocRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userDocRef);

      if (userSnap.exists()) {
        const { name, profileName, displayName } = userSnap.data();
        navigation.setOptions({ title: name || 'Messages' });
        setChatPartnerName(name);
      } else {
        navigation.setOptions({ title: 'Messages no user' });
      }
    };

    if (userId && currentUser) {
      const functions = getFunctions();
      const createOrFindChat = httpsCallable(functions, 'createOrFindChat');
      createOrFindChat({ otherUserId: userId, currentUserId: currentUser.uid })
        .then((result: any) => {
          setChatId(result.data.chatId);
        })
        .catch((error) => {
          console.error('Error creating or finding chat:', error);
        });
    }

    const handleLeaveChat = () => {
      const chatPartnerNameText = chatPartnerName || 'this user';

      Alert.alert(
        'Leave Chat',
        `Are you sure you want to leave chat with ${chatPartnerNameText}?`,
        [
          { text: 'No', style: 'cancel' },
          {
            text: 'Yes',
            onPress: async () => {
              try {
                if (!currentUser || !chatId) return;
                
                const functions = getFunctions();
                const leaveChat = httpsCallable(functions, 'leaveChat');
                const dislikeProfile = httpsCallable(functions, 'dislikeProfile');
                const leaveChatResult = await leaveChat({chatId: chatId});
                dislikeProfile({ userId: currentUser.uid, dislikedUserId: userId });

              } catch (error) {
                console.warn('Error leaving chat:', error);
              }

              navigation.goBack();
            },
          },
        ]
      );
    }

    navigation.setOptions({ title: fetchUserName() || 'Messages' });

    navigation.setOptions( {headerRight: () => (
      <TouchableOpacity onPress={handleLeaveChat} style={styles.leaveButton}>
          <Text style={styles.leaveButtonText}>Leave Chat</Text>
      </TouchableOpacity>
    )});

  }, [navigation, currentUser, userId, chatId]);

  // Listen for real-time message updates in Firestore
  useEffect(() => {
    if (!chatId) {
      setLoading(false);
      return;
    }

    const db = getFirestore();
    const messagesRef = collection(db, 'chats', chatId, 'messages');
    const q = query(messagesRef, orderBy('timestamp', 'asc'));

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const updatedMessages = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Message[];
      
      // Update messages, but filter out any pending messages that have been confirmed
      setMessages(prevMessages => {
        const pendingMessages = prevMessages.filter(msg => 
          msg.pending && !updatedMessages.some(serverMsg => 
            serverMsg.text === msg.text && 
            serverMsg.senderId === msg.senderId
          )
        );
        
        return [...updatedMessages, ...pendingMessages];
      });
      
      setLoading(false);
    });

    return () => unsubscribe();
  }, [chatId]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !chatId) return;
    if (!currentUser) {
      console.warn('No current user found.');
      return;
    }

    const messageText = newMessage.trim();
    setNewMessage(''); // Clear input immediately
    
    // Generate a temporary ID for the pending message
    const tempId = `pending-${Date.now()}`;
    
    // Create a pending message to show immediately
    const pendingMessage: Message = {
      id: tempId,
      text: messageText,
      senderId: currentUser.uid,
      timestamp: Timestamp.now(),
      pending: true,
    };
    
    // Add the pending message to the messages array
    setMessages(prevMessages => [...prevMessages, pendingMessage]);

    try {
      const functions = getFunctions();
      const sendMessageFn = httpsCallable(functions, 'sendMessage');
      await sendMessageFn({
        chatId,
        text: messageText,
        currentUserId: currentUser.uid,
      });
      
      // The message has been sent, but we don't need to do anything here
      // because the Firestore listener will update with the confirmed message
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Remove the pending message if there was an error
      setMessages(prevMessages => 
        prevMessages.filter(msg => msg.id !== tempId)
      );
      
      // Optionally, show an error message
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={90} // Adjust this value based on your header height
    >
      <FlatList
        data={messages}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <MessageItem item={item} currentUserUid={currentUser?.uid} />
        )}
        style={styles.messageList}
        inverted={false}
        // Add this to automatically scroll to bottom when new messages arrive
        onContentSizeChange={() => {
          messages.length > 0 && flatListRef.current?.scrollToEnd({animated: true});
        }}
        onLayout={() => {
          messages.length > 0 && flatListRef.current?.scrollToEnd({animated: true});
        }}
        ref={flatListRef}
      />
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Type a message..."
          placeholderTextColor={Colors.textTertiary}
          value={newMessage}
          onChangeText={setNewMessage}
        />
        <TouchableOpacity onPress={handleSendMessage} style={styles.sendButton}>
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

export default MessagingShowPage;

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    padding: 10,
    backgroundColor: Colors.background,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageList: { flex: 1 },
  messageItem: {
    padding: 12,
    borderRadius: 12,
    marginVertical: 6,
    maxWidth: '80%',
  },
  yourMessage: { 
    alignSelf: 'flex-end', 
    backgroundColor: Colors.primary,
  },
  theirMessage: { 
    alignSelf: 'flex-start', 
    backgroundColor: Colors.backgroundGray, 
    borderWidth: 1,
    borderColor: Colors.borderLight,
  },
  messageText: { 
    fontSize: 16 
  },
  yourMessageText: {
    color: Colors.textLight,
  },
  theirMessageText: {
    color: Colors.text,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    padding: 10,
  },
  input: {
    flex: 1,
    height: 40,
    borderColor: Colors.border,
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 12,
    backgroundColor: Colors.backgroundSecondary,
    color: Colors.text,
  },
  sendButton: {
    marginLeft: 10,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  sendButtonText: { 
    color: Colors.textLight, 
    fontSize: 16,
    fontWeight: '600',
  },
  leaveButton: {
    marginRight: 10,
  },
  leaveButtonText: {
    color: Colors.error,
    fontSize: 16,
    fontWeight: '600',
  },
  pendingMessage: {
    opacity: 0.7,
  },
  pendingMessageText: {
    fontStyle: 'italic',
  },
  keyboardView: {
    flex: 1,
  },
  keyboardContent: {
    flexGrow: 1,
  },
});