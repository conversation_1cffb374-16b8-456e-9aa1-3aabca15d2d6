import {StyleSheet, View, ScrollView, SafeAreaView} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import YourProfilePreview from '@/components/YourProfilePreview';
import YourGroupsPreview from '@/components/YourGroupsPreview';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Colors from '@/constants/Colors';

// Define navigation params type
type RootStackParamList = {
    Groups: undefined;
    // Add other routes as needed
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList, 'Groups'>;

export default function Index() {
    const navigation = useNavigation<NavigationProp>();

    const handleJoinGroupPress = () => {
        navigation.navigate('Groups');
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView
                style={styles.scrollContainer}
                contentContainerStyle={styles.contentContainer}
                showsVerticalScrollIndicator={false}
            >
                <YourProfilePreview />
                <YourGroupsPreview />
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    scrollContainer: {
        flex: 1,
    },
    contentContainer: {
        padding: 20,
        paddingTop: 40,
    },
});
