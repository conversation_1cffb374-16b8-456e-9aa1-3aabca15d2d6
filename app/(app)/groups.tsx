import {functions} from '@/config/firebaseConfig';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getAuth} from 'firebase/auth';
import {httpsCallable} from 'firebase/functions';
import {callFirebaseFunction, waitForAuth} from '@/utils/firebaseUtils';
import React, {
    useCallback,
    useEffect,
    useLayoutEffect,
    useRef,
    useState,
} from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import Colors from '@/constants/Colors';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

interface Group {
    id: string;
    name: string;
    location: string;
    users: number;
    isMember?: boolean;
}

interface GroupsResponse {
    groups: Group[];
    hasMore: boolean;
    lastGroupId: string | null;
    lastGroupName: string | null;
}

// Define navigation params type
type RootStackParamList = {
    Group: {
        groupId: string;
        groupName: string;
        groupLocation: string;
    };
    GroupCreate: undefined;
    // Add other routes as needed
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const Groups = () => {
    const [search, setSearch] = useState<string>('');
    const [groups, setGroups] = useState<Group[]>([]);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [lastGroupId, setLastGroupId] = useState<string | null>(null);
    const [lastGroupName, setLastGroupName] = useState<string | null>(null);
    const navigation = useNavigation<NavigationProp>();
    const auth = getAuth();
    const user = auth.currentUser;
    const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    useLayoutEffect(() => {
        navigation.setOptions({
            headerRight: () => (
                <TouchableOpacity
                    onPress={() => navigation.navigate('GroupCreate')}>
                    <Text style={styles.createButton}>Create</Text>
                </TouchableOpacity>
            ),
        });
    }, [navigation]);

    useEffect(() => {
        fetchRecommendedGroups();
    }, [user]);

    const fetchRecommendedGroups = async () => {
        console.log('=== CLIENT AUTH DEBUG ===');
        console.log('user exists:', !!user);
        console.log('user.uid:', user?.uid);
        console.log('user object:', user);
        
        if (!user?.uid) {
            console.error('User is not authenticated');
            return;
        }

        setLoading(true);

        try {
            console.log('About to call getGroups function...');
            
            // Use the new utility function with proper authentication handling
            const response = await callFirebaseFunction<Group[]>(functions, 'getGroups', {
                limit: 10
            }, user);

            console.log('getGroups succeeded:', response);
            setGroups(response);
            setHasMore(false); // Recommended groups don't have pagination
            setLastGroupId(null);
            setLastGroupName(null);
        } catch (error: any) {
            console.error('Error fetching recommended groups: ', error);
            Alert.alert('Error', error.message || 'Failed to fetch recommended groups.');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const fetchGroups = async (query: string, reset: boolean = false) => {
        if (!user?.uid) {
            console.error('User is not authenticated');
            return;
        }

        if (reset) {
            setLoading(true);
        } else if (!reset && !loadingMore) {
            setLoadingMore(true);
        }

        try {
            let result;
            
            // Use getGroups for initial load (no search query) and searchGroups for actual searching
            if (!query.trim()) {
                // Initial load: get recommended groups based on user profile
                const groups = await callFirebaseFunction<Group[]>(functions, 'getGroups', {
                    limit: 10,
                }, user);
                
                if (reset) {
                    setGroups(groups);
                } else {
                    setGroups(prevGroups => [...prevGroups, ...groups]);
                }
                
                // For getGroups, we don't have pagination info
                setHasMore(false);
                setLastGroupId(null);
                setLastGroupName(null);
            } else {
                // Search mode: use searchGroups for actual search queries
                const response = await callFirebaseFunction<GroupsResponse>(functions, 'searchGroups', {
                    query: query,
                    limit: 10,
                    lastGroupId: reset ? null : lastGroupId,
                    lastGroupName: reset ? null : lastGroupName,
                }, user);

                if (reset) {
                    setGroups(response.groups);
                } else {
                    setGroups(prevGroups => [...prevGroups, ...response.groups]);
                }

                setHasMore(response.hasMore);
                setLastGroupId(response.lastGroupId);
                setLastGroupName(response.lastGroupName);
            }
        } catch (error) {
            console.error('Error fetching groups: ', error);
            Alert.alert('Error', 'Failed to fetch groups.');
        } finally {
            setLoading(false);
            setRefreshing(false);
            setLoadingMore(false);
        }
    };

    const handleSearch = (text: string) => {
        setSearch(text);

        if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
        }

        searchTimeoutRef.current = setTimeout(() => {
            setLastGroupId(null);
            setLastGroupName(null);
            setHasMore(true);
            fetchGroups(text, true);
        }, 500);
    };

    const handleRefresh = () => {
        setRefreshing(true);
        setLastGroupId(null);
        setLastGroupName(null);
        setHasMore(true);
        
        if (search.trim()) {
            fetchGroups(search, true);
        } else {
            fetchRecommendedGroups();
        }
    };

    const handleLoadMore = () => {
        // Only allow load more for search results, not for recommendations
        if (!loading && !loadingMore && hasMore && search.trim()) {
            fetchGroups(search, false);
        }
    };

    useFocusEffect(
        useCallback(() => {
            setLastGroupId(null);
            setLastGroupName(null);
            setHasMore(true);
            fetchRecommendedGroups();
            
            return () => {
                if (searchTimeoutRef.current) {
                    clearTimeout(searchTimeoutRef.current);
                }
            };
        }, []),
    );

    const handleJoinGroup = async (group: Group) => {
        if (user) {
            try {
                const joinGroupFunction = httpsCallable(functions, 'joinGroup');
                await joinGroupFunction({
                    groupId: group.id,
                });

                navigation.navigate('Group', {
                    groupId: group.id,
                    groupName: group.name,
                    groupLocation: group.location,
                });
            } catch (error) {
                console.error('Error joining group: ', error);
                Alert.alert('Error', 'Failed to join the group.');
            }
        }
    };

    const renderItem = ({item}: {item: Group}) => (
        <View style={styles.item}>
            <View style={styles.itemText}>
                <Text style={styles.name}>{item.name}</Text>
                <Text style={styles.location}>{item.location}</Text>
                <Text style={styles.users}>
                    {item.users} {item.users === 1 ? 'user' : 'users'}
                </Text>
            </View>
            <TouchableOpacity
                style={[
                    styles.actionButton,
                    item.isMember ? styles.viewButton : styles.joinButton,
                ]}
                onPress={() => {
                    if (item.isMember) {
                        navigation.navigate('Group', {
                            groupId: item.id,
                            groupName: item.name,
                            groupLocation: item.location,
                        });
                    } else {
                        handleJoinGroup(item);
                    }
                }}>
                <Text style={styles.buttonText}>
                    {item.isMember ? 'View' : 'Join'}
                </Text>
            </TouchableOpacity>
        </View>
    );

    const renderCreateGroupItem = () => (
        <TouchableOpacity
            onPress={() => navigation.navigate('GroupCreate')}
            style={styles.createItem}>
            <Text style={styles.createGroupText}>Create Group</Text>
        </TouchableOpacity>
    );

    const renderFooter = () => {
        if (!loadingMore) return null;
        return (
            <View style={styles.footerLoader}>
                <ActivityIndicator size="small" />
            </View>
        );
    };

    const renderHeader = () => (
        <TextInput
            style={styles.searchBar}
            placeholder="Search Groups"
            value={search}
            onChangeText={handleSearch}
            placeholderTextColor={Colors.textTertiary}
        />
    );

    return (
        <View style={styles.outerContainer}>
            <KeyboardAvoidingView 
                style={styles.container}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
            >
                {loading ? (
                    <View style={styles.loadingContainer}>
                        {renderHeader()}
                        <ActivityIndicator color={Colors.primary} size="large" />
                    </View>
                ) : (
                    <FlatList
                        data={[
                            ...groups,
                            {
                                id: 'create',
                                name: 'Create Group',
                                location: '',
                                users: 0,
                                isMember: false,
                            } as Group,
                        ]}
                        renderItem={({item}) =>
                            item.id === 'create'
                                ? renderCreateGroupItem()
                                : renderItem({item})
                        }
                        keyExtractor={item => item.id}
                        onRefresh={handleRefresh}
                        refreshing={refreshing}
                        onEndReached={handleLoadMore}
                        onEndReachedThreshold={0.5}
                        ListHeaderComponent={renderHeader}
                        ListFooterComponent={renderFooter}
                        keyboardShouldPersistTaps="handled"
                        keyboardDismissMode="on-drag"
                        contentContainerStyle={styles.flatListContent}
                        automaticallyAdjustKeyboardInsets={true}
                    />
                )}
            </KeyboardAvoidingView>
        </View>
    );
};

const styles = StyleSheet.create({
    outerContainer: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    container: {
        flex: 1,
        padding: 20,
    },
    loadingContainer: {
        flex: 1,
    },
    flatListContent: {
        flexGrow: 1,
        paddingBottom: 20,
    },
    searchBar: {
        height: 40,
        borderColor: Colors.border,
        borderWidth: 1,
        borderRadius: 10,
        marginBottom: 20,
        paddingHorizontal: 10,
        backgroundColor: Colors.backgroundSecondary,
    },
    item: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
        marginBottom: 12,
        backgroundColor: Colors.backgroundGray,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: Colors.borderLight,
    },
    createItem: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
        marginBottom: 12,
        backgroundColor: Colors.backgroundGray,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: Colors.borderLight,
    },
    itemText: {
        flex: 1,
    },
    name: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.text,
        marginBottom: 4,
    },
    location: {
        fontSize: 15,
        color: Colors.primaryDark,
        marginBottom: 2,
    },
    users: {
        fontSize: 14,
        color: Colors.textSecondary,
    },
    createGroupText: {
        fontSize: 18,
        fontWeight: '600',
        color: Colors.primary,
    },
    createButton: {
        fontSize: 16,
        color: Colors.primary,
        marginRight: 10,
        fontWeight: '600',
    },
    actionButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 10,
        minWidth: 80,
        alignItems: 'center',
    },
    joinButton: {
        backgroundColor: Colors.primary,
    },
    viewButton: {
        backgroundColor: Colors.primary,
    },
    buttonText: {
        color: Colors.textLight,
        fontWeight: '600',
        fontSize: 14,
    },
    footerLoader: {
        marginVertical: 16,
        alignItems: 'center',
    },
});

export default Groups;
