import React from 'react';
import { View, Text, StyleSheet, Button, Linking } from 'react-native';

const Support = () => {
    const handleEmailSupport = () => {
        Linking.openURL('mailto:<EMAIL>');
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Support</Text>
            <Text style={styles.description}>
                If you have any issues, please contact our support team.
            </Text>
            <Button title="Email Support" onPress={handleEmailSupport} />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
    },
    description: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 20,
    },
});

export default Support;