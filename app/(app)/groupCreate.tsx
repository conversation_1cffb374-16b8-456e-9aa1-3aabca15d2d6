import React, {useEffect, useRef, useState} from 'react';
import {
    ActivityIndicator,
    Animated,
    FlatList,
    Keyboard,
    Modal,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {
    addDoc,
    collection,
    doc,
    getDocs,
    getFirestore,
    query,
    setDoc,
    where,
} from 'firebase/firestore';
import {getAuth} from 'firebase/auth';
import {getFunctions, httpsCallable} from 'firebase/functions';
import Toast from 'react-native-toast-message';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {StackActions} from '@react-navigation/native';
import Colors from '@/constants/Colors';

interface Location {
    id: string;
    name: string;
}

interface Group {
    id: string;
    name: string;
    location: string;
    locationId: string;
    users: number;
    isMember: boolean;
}

interface SearchGroupsResponse {
    data: Group[];
}

type RootStackParamList = {
    GroupCreate: undefined;
    Group: {groupId: string; groupName: string; groupLocation: string};
    [key: string]: undefined | object;
};

type GroupCreateScreenProps = {
    navigation: NativeStackNavigationProp<RootStackParamList, 'GroupCreate'>;
};

const GroupCreateScreen = ({navigation}: GroupCreateScreenProps) => {
    const locationInputRef = useRef<TextInput>(null);
    const groupNameInputRef = useRef<TextInput>(null);
    const [step, setStep] = useState(1); // 1 for location, 2 for group name
    const [locationInput, setLocationInput] = useState('');
    const [groupName, setGroupName] = useState('');
    const [locations, setLocations] = useState<Location[]>([]);
    const [matchingGroups, setMatchingGroups] = useState<Group[]>([]);
    const [selectedLocation, setSelectedLocation] = useState<Location | null>(
        null,
    );
    const [alertOpacity] = useState(new Animated.Value(0));
    const [joinGroupModal, setJoinGroupModal] = useState(false);
    const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
    const [locationSearchLoading, setLocationSearchLoading] = useState(false);
    const [groupSearchLoading, setGroupSearchLoading] = useState(false);
    const [isCreatingGroup, setIsCreatingGroup] = useState(false);
    const [isCreatingLocation, setIsCreatingLocation] = useState(false);

    const db = getFirestore();
    const functions = getFunctions();
    const searchGroupsFunction = httpsCallable<
        {query: string; userId: string | undefined; returnFormat?: string},
        {groups: Group[]}
    >(functions, 'searchGroups');

    useEffect(() => {
        navigation.setOptions({
            title: 'Create Group',
        });

        // Focus the location input when component mounts
        if (step === 1 && locationInputRef.current) {
            setTimeout(() => locationInputRef.current?.focus(), 300);
        } else if (step === 2 && groupNameInputRef.current) {
            setTimeout(() => groupNameInputRef.current?.focus(), 300);
        }
    }, [navigation, step]);

    useEffect(() => {
        const searchLocations = async () => {
            if (locationInput.trim().length < 2) {
                setLocations([]);
                return;
            }

            setLocationSearchLoading(true);
            try {
                const locationQuery = query(
                    collection(db, 'Locations'),
                    where('name', '>=', locationInput),
                    where('name', '<=', locationInput + '\uf8ff'),
                );
                const snapshot = await getDocs(locationQuery);

                const locationResults = snapshot.docs.map(doc => ({
                    id: doc.id,
                    name: doc.data().name,
                }));

                setLocations(locationResults);
            } catch (error) {
                console.error('Error searching locations: ', error);
            } finally {
                setLocationSearchLoading(false);
            }
        };

        const timeoutId = setTimeout(() => {
            searchLocations();
        }, 500);

        return () => clearTimeout(timeoutId);
    }, [locationInput, db]);

    useEffect(() => {
        const searchGroups = async () => {
            if (!selectedLocation || groupName.trim().length < 2) {
                setMatchingGroups([]);
                return;
            }

            setGroupSearchLoading(true);
            try {
                const auth = getAuth();
                const userId = auth.currentUser?.uid;

                const result = await searchGroupsFunction({
                    query: groupName,
                    userId: userId,
                });

                const filteredGroups = result.data.groups.filter(
                    (group: Group) => group.locationId === selectedLocation.id,
                );

                setMatchingGroups(filteredGroups);
            } catch (error) {
                console.error('Error searching groups: ', error);
            } finally {
                setGroupSearchLoading(false);
            }
        };

        searchGroups();
    }, [groupName, selectedLocation]);

    const selectLocation = async (location: Location) => {
        setSelectedLocation(location);
        setStep(2);
    };

    const createNewLocation = async () => {
        if (!locationInput.trim() || isCreatingLocation) return;

        setIsCreatingLocation(true);
        try {
            const newLocationRef = await addDoc(collection(db, 'Locations'), {
                name: locationInput.trim(),
                createdAt: new Date(),
            });

            const newLocation: Location = {
                id: newLocationRef.id,
                name: locationInput.trim(),
            };

            setSelectedLocation(newLocation);
            setStep(2);
        } catch (error) {
            console.error('Error creating new location: ', error);
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: 'Failed to create location. Please try again.',
            });
        } finally {
            setIsCreatingLocation(false);
        }
    };

    const handleGroupSelect = (group: Group) => {
        setSelectedGroup(group);
        setJoinGroupModal(true);
    };

    const handleJoinGroup = async () => {
        Keyboard.dismiss();
        setJoinGroupModal(false);

        if (!selectedGroup) return;

        const auth = getAuth();
        const user = auth.currentUser;

        if (user) {
            try {
                const groupUserDoc = doc(
                    collection(db, `Groups/${selectedGroup.id}/Group_Users`),
                    user.uid,
                );

                await setDoc(groupUserDoc, {
                    joinedAt: new Date(),
                    isCreator: false,
                });

                Toast.show({
                    type: 'success',
                    text1: 'Success',
                    text2: 'Joined group successfully',
                });

                navigation.dispatch(
                    StackActions.replace('Group', {
                        groupId: selectedGroup.id,
                        groupName: selectedGroup.name,
                        groupLocation: selectedGroup.location,
                    }),
                );
            } catch (error) {
                console.error('Error joining group:', error);
                Toast.show({
                    type: 'error',
                    text1: 'Error',
                    text2: 'Failed to join group',
                });
            }
        }
    };

    const handleCreateGroup = async () => {
        Keyboard.dismiss();

        if (!groupName.trim() || !selectedLocation || isCreatingGroup) return;

        const auth = getAuth();
        const user = auth.currentUser;
        if (user) {
            setIsCreatingGroup(true);
            try {
                // Use the Firebase function instead of direct Firestore writes
                const functions = getFunctions();
                const createGroupFunction = httpsCallable(functions, 'createGroup');
                
                const result = await createGroupFunction({
                    userId: user.uid,
                    groupName: groupName.trim(),
                    location: selectedLocation.name
                });

                const groupData = result.data as any;
                
                if (groupData.success) {
                    Toast.show({
                        type: 'success',
                        text1: 'Success',
                        text2: 'Group created successfully',
                    });

                    setTimeout(() => {
                        navigation.dispatch(
                            StackActions.replace('Group', {
                                groupId: groupData.groupId,
                                groupName: groupData.groupName,
                                groupLocation: selectedLocation.name,
                            }),
                        );
                    }, 1000);
                } else {
                    throw new Error('Failed to create group');
                }
            } catch (error) {
                console.error('Error creating group: ', error);
                Toast.show({
                    type: 'error',
                    text1: 'Error',
                    text2: 'Failed to create group. Please try again.',
                });
            } finally {
                setIsCreatingGroup(false);
            }
        }
    };

    const renderLocationItem = ({item}: {item: Location}) => (
        <TouchableOpacity
            style={styles.listItem}
            onPress={() => selectLocation(item)}>
            <Text style={styles.itemName}>{item.name}</Text>
        </TouchableOpacity>
    );

    const renderGroupItem = ({item}: {item: Group}) => (
        <TouchableOpacity
            style={styles.listItem}
            onPress={() => handleGroupSelect(item)}>
            <View>
                <Text style={styles.itemName}>{item.name}</Text>
                <Text style={styles.subText}>Members: {item.users}</Text>
            </View>
            <TouchableOpacity
                style={styles.joinButton}
                onPress={() => handleGroupSelect(item)}>
                <Text style={styles.joinButtonText}>Join</Text>
            </TouchableOpacity>
        </TouchableOpacity>
    );

    return (
        <KeyboardAwareScrollView
            style={styles.container}
            enableOnAndroid={true}
            enableAutomaticScroll={true}
            keyboardShouldPersistTaps="handled"
            extraScrollHeight={20}>
            {step === 1 ? (
                <>
                    <Text style={styles.label}>Group Location</Text>
                    <TextInput
                        ref={locationInputRef}
                        style={styles.input}
                        value={locationInput}
                        onChangeText={setLocationInput}
                        placeholder="Enter group location"
                        placeholderTextColor={Colors.textTertiary}
                        autoFocus={true}
                    />

                    {locationSearchLoading ? (
                        <View style={styles.loadingContainer}>
                            <ActivityIndicator
                                color={Colors.primary}
                                size="small"
                            />
                            <Text style={styles.loadingText}>
                                Searching locations...
                            </Text>
                        </View>
                    ) : (
                        <>
                            {locations.length > 0 ? (
                                <>
                                    <Text style={styles.subLabel}>
                                        Suggested locations:
                                    </Text>
                                    <FlatList
                                        data={locations}
                                        renderItem={renderLocationItem}
                                        keyExtractor={item => item.id}
                                        style={styles.list}
                                    />
                                    {locationInput.trim().length >= 2 && (
                                        <View
                                            style={
                                                styles.createButtonContainer
                                            }>
                                            <TouchableOpacity
                                                style={[
                                                    styles.createButton,
                                                    isCreatingLocation && styles.createButtonDisabled
                                                ]}
                                                onPress={createNewLocation}
                                                disabled={isCreatingLocation}>
                                                {isCreatingLocation ? (
                                                    <View style={styles.loadingButtonContent}>
                                                        <ActivityIndicator
                                                            color="#FFFFFF"
                                                            size="small"
                                                        />
                                                        <Text
                                                            style={[
                                                                styles.createButtonText,
                                                                styles.loadingButtonText
                                                            ]}>
                                                            Creating...
                                                        </Text>
                                                    </View>
                                                ) : (
                                                    <Text
                                                        style={
                                                            styles.createButtonText
                                                        }>
                                                        Create New Location
                                                    </Text>
                                                )}
                                            </TouchableOpacity>
                                        </View>
                                    )}
                                </>
                            ) : locationInput.trim().length >= 2 ? (
                                <View style={styles.noResultsContainer}>
                                    <Text style={styles.noResultsText}>
                                        No locations found
                                    </Text>
                                    <View style={styles.createButtonContainer}>
                                        <TouchableOpacity
                                            style={[
                                                styles.createButton,
                                                isCreatingLocation && styles.createButtonDisabled
                                            ]}
                                            onPress={createNewLocation}
                                            disabled={isCreatingLocation}>
                                            {isCreatingLocation ? (
                                                <View style={styles.loadingButtonContent}>
                                                    <ActivityIndicator
                                                        color="#FFFFFF"
                                                        size="small"
                                                    />
                                                    <Text
                                                        style={[
                                                            styles.createButtonText,
                                                            styles.loadingButtonText
                                                        ]}>
                                                        Creating...
                                                    </Text>
                                                </View>
                                            ) : (
                                                <Text
                                                    style={styles.createButtonText}>
                                                    Create New Location
                                                </Text>
                                            )}
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            ) : null}
                        </>
                    )}
                </>
            ) : (
                <>
                    <View style={styles.locationDisplay}>
                        <Text style={styles.label}>
                            Selected Location: {selectedLocation?.name}
                        </Text>
                        <TouchableOpacity onPress={() => setStep(1)}>
                            <Text style={styles.changeButton}>Change</Text>
                        </TouchableOpacity>
                    </View>

                    <Text style={styles.label}>Group Name</Text>
                    <TextInput
                        ref={groupNameInputRef}
                        style={styles.input}
                        value={groupName}
                        onChangeText={setGroupName}
                        placeholder="Enter group name"
                        placeholderTextColor={Colors.textTertiary}
                        autoFocus={false}
                    />

                    {groupSearchLoading ? (
                        <View style={styles.loadingContainer}>
                            <ActivityIndicator
                                color={Colors.primary}
                                size="small"
                            />
                            <Text style={styles.loadingText}>
                                Searching groups...
                            </Text>
                        </View>
                    ) : (
                        <>
                            {matchingGroups.length > 0 && (
                                <>
                                    <Text style={styles.subLabel}>
                                        Similar groups found at this location:
                                    </Text>
                                    <FlatList
                                        data={matchingGroups}
                                        renderItem={renderGroupItem}
                                        keyExtractor={item => item.id}
                                        style={styles.list}
                                    />
                                </>
                            )}

                            {groupName.trim().length >= 2 &&
                                matchingGroups.length === 0 && (
                                    <Text style={styles.noGroupsText}>
                                        No existing groups match this name
                                    </Text>
                                )}

                            {groupName.trim().length >= 2 &&
                                !groupSearchLoading && (
                                    <View style={styles.createButtonContainer}>
                                        <TouchableOpacity
                                            style={[
                                                styles.createButton,
                                                isCreatingGroup && styles.createButtonDisabled
                                            ]}
                                            onPress={handleCreateGroup}
                                            disabled={isCreatingGroup}>
                                            {isCreatingGroup ? (
                                                <View style={styles.loadingButtonContent}>
                                                    <ActivityIndicator
                                                        color="#FFFFFF"
                                                        size="small"
                                                    />
                                                    <Text
                                                        style={[
                                                            styles.createButtonText,
                                                            styles.loadingButtonText
                                                        ]}>
                                                        Creating...
                                                    </Text>
                                                </View>
                                            ) : (
                                                <Text
                                                    style={styles.createButtonText}>
                                                    Create New Group
                                                </Text>
                                            )}
                                        </TouchableOpacity>
                                    </View>
                                )}
                        </>
                    )}
                </>
            )}

            <Animated.View
                style={[styles.alertContainer, {opacity: alertOpacity}]}>
                <Text style={styles.alertText}>Group created successfully</Text>
            </Animated.View>

            <Modal
                visible={joinGroupModal}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setJoinGroupModal(false)}>
                <View style={styles.modalContainer}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Join Group?</Text>
                        <Text style={styles.modalText}>
                            Would you like to join "{selectedGroup?.name}"?
                        </Text>
                        <View style={styles.modalButtons}>
                            <TouchableOpacity
                                style={styles.modalButton}
                                onPress={() => setJoinGroupModal(false)}>
                                <Text style={styles.buttonText}>No</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.modalButton,
                                    styles.primaryButton,
                                ]}
                                onPress={handleJoinGroup}>
                                <Text style={styles.primaryButtonText}>
                                    Yes
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </KeyboardAwareScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: 20,
        backgroundColor: Colors.background,
    },
    label: {
        fontSize: 18,
        marginBottom: 10,
        fontWeight: '500',
        color: Colors.text,
    },
    subLabel: {
        fontSize: 16,
        marginTop: 10,
        marginBottom: 5,
        color: Colors.textSecondary,
    },
    input: {
        height: 45,
        borderColor: Colors.border,
        borderWidth: 1,
        borderRadius: 10,
        marginBottom: 15,
        paddingHorizontal: 12,
        backgroundColor: Colors.backgroundSecondary,
        color: Colors.text,
    },
    list: {
        borderColor: Colors.borderLight,
        borderWidth: 1,
        borderRadius: 10,
        backgroundColor: Colors.backgroundGray,
        marginBottom: 15,
    },
    listItem: {
        padding: 15,
        borderBottomColor: Colors.borderLight,
        borderBottomWidth: 1,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    noResultsContainer: {
        alignItems: 'center',
        padding: 20,
    },
    locationDisplay: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingBottom: 15,
        marginBottom: 15,
        borderBottomColor: Colors.borderLight,
        borderBottomWidth: 1,
    },
    changeButton: {
        color: Colors.primary,
        fontWeight: '500',
    },
    subText: {
        fontSize: 14,
        color: Colors.textSecondary,
        marginTop: 4,
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 10,
    },
    loadingText: {
        marginLeft: 10,
        textAlign: 'center',
        color: Colors.textSecondary,
    },
    noGroupsText: {
        margin: 10,
        textAlign: 'center',
        color: Colors.textSecondary,
        marginBottom: 20,
    },
    noResultsText: {
        marginBottom: 10,
        color: Colors.textSecondary,
    },
    alertContainer: {
        position: 'absolute',
        bottom: 20,
        left: 0,
        right: 0,
        alignItems: 'center',
    },
    alertText: {
        backgroundColor: Colors.success,
        color: Colors.textLight,
        padding: 12,
        borderRadius: 8,
        fontWeight: '600',
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
        width: '80%',
        backgroundColor: Colors.background,
        borderRadius: 12,
        padding: 24,
        shadowColor: Colors.shadow,
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 16,
        color: Colors.text,
        textAlign: 'center',
    },
    modalText: {
        marginBottom: 24,
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: 'center',
    },
    modalButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    modalButton: {
        padding: 12,
        borderRadius: 8,
        width: '45%',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.borderLight,
    },
    primaryButton: {
        backgroundColor: Colors.primary,
        borderColor: Colors.primary,
    },
    buttonText: {
        color: Colors.textSecondary,
        fontWeight: '600',
    },
    primaryButtonText: {
        color: Colors.textLight,
        fontWeight: '600',
    },
    createButtonContainer: {
        marginTop: 20,
        marginBottom: 20,
    },
    createButton: {
        backgroundColor: Colors.primary,
        padding: 15,
        borderRadius: 10,
        alignItems: 'center',
    },
    createButtonText: {
        color: Colors.textLight,
        fontWeight: '600',
        fontSize: 16,
    },
    createButtonDisabled: {
        backgroundColor: Colors.textTertiary,
        opacity: 0.6,
    },
    loadingButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingButtonText: {
        marginLeft: 8,
    },
    itemName: {
        fontSize: 16,
        fontWeight: '500',
        color: Colors.text,
    },
    joinButton: {
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 8,
        backgroundColor: Colors.primary,
    },
    joinButtonText: {
        color: Colors.textLight,
        fontWeight: '600',
        fontSize: 14,
    },
});

export default GroupCreateScreen;
