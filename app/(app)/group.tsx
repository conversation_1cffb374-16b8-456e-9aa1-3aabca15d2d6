import React, {useEffect, useState} from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
    collection,
    deleteDoc,
    doc,
    getCountFromServer,
    getFirestore,
} from 'firebase/firestore';
import {getFunctions, httpsCallable} from 'firebase/functions';
import {getAuth} from 'firebase/auth';
import UserProfileCard from '@/components/UserProfileCard';
import Colors from '@/constants/Colors';
import { handleGroupLeave, requestReview } from '@/utils/appReview';

interface GroupParams {
    groupId: string;
    groupName: string;
}

interface UserProfile {
    id: string;
    profileName: string;
    birthDate: number;
    location: string;
    images: string | string[];
    description: string;
    [key: string]: any; // Keep this for any additional properties
}

const Group = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const {groupId, groupName} = route.params as GroupParams;
    const [currentProfiles, setCurrentProfiles] = useState<UserProfile[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingMore, setLoadingMore] = useState(false);
    const [hasMoreData, setHasMoreData] = useState(true);
    const [page, setPage] = useState(1);
    const [limit] = useState(10);

    const auth = getAuth();
    const currentUser = auth.currentUser;

    useEffect(() => {
        navigation.setOptions({
            title: groupName || 'Group',
            headerRight: () => (
                <TouchableOpacity onPress={handleLeaveGroup}>
                    <Text style={styles.leaveButton}>Leave</Text>
                </TouchableOpacity>
            ),
        });
    }, [navigation, groupName]);

    useEffect(() => {
        fetchGroupUsers(1, false);
    }, [groupId]);

    const fetchGroupUsers = async (pageToFetch = 1, shouldAppend = false) => {
        if (!currentUser) return;

        if (pageToFetch === 1) {
            setLoading(true);
        } else {
            setLoadingMore(true);
        }

        const functions = getFunctions();
        const getGroup = httpsCallable(functions, 'getGroup');

        try {
            const result: any = await getGroup({
                groupId,
                currentUserId: currentUser.uid,
                page: pageToFetch,
                limit,
            });

            const profilesData = result.data || [];

            if (profilesData.length < limit) {
                setHasMoreData(false);
            }

            if (shouldAppend) {
                setCurrentProfiles(prevProfiles => [
                    ...prevProfiles,
                    ...profilesData,
                ]);
            } else {
                setCurrentProfiles(profilesData);
            }
        } catch (error) {
            console.error('Error fetching group users: ', error);
            Alert.alert('Error', 'Failed to fetch group users.');
        } finally {
            setLoading(false);
            setLoadingMore(false);
        }
    };

    const loadMoreProfiles = () => {
        if (hasMoreData && !loadingMore) {
            const nextPage = page + 1;
            setPage(nextPage);
            fetchGroupUsers(nextPage, true);
        }
    };

    const handleLeaveGroup = async () => {
        if (currentUser) {

            const db = getFirestore();
            const userDocRef = doc(
                db,
                `Groups/${groupId}/Group_Users`,
                currentUser.uid,
            );
            try {
                await deleteDoc(userDocRef);

                const groupUsersRef = collection(
                    db,
                    `Groups/${groupId}/Group_Users`,
                );
                const count = await getCountFromServer(groupUsersRef);
                if (count.data().count === 0) {
                    // If empty, remove the entire Group document
                    await deleteDoc(doc(db, 'Groups', groupId));
                }

                // Check if we should show the app review dialog
                const shouldShowReview = await handleGroupLeave();
                if (shouldShowReview) {
                    // Wait a moment before showing the review dialog so 
                    // the user can see they've successfully left the group
                    setTimeout(() => {
                        requestReview();
                    }, 1000);
                }

                navigation.goBack();
            } catch (error) {
                console.error('Error leaving group: ', error);
                Alert.alert('Error', 'Failed to leave the group.');
            }
        }
    };

    const handleLikeProfile = async (id: string) => {
        setCurrentProfiles(prevProfiles =>
            prevProfiles.filter(profile => profile.id !== id),
        );
    };

    const handleRemoveProfile = (id: string) => {
        setCurrentProfiles(prevProfiles =>
            prevProfiles.filter(profile => profile.id !== id),
        );
    };

    const renderFooter = () => {
        if (!loadingMore) return null;
        return (
            <View style={styles.footerLoader}>
                <ActivityIndicator size="small" />
            </View>
        );
    };

    const {width} = Dimensions.get('window');

    return (
        <View style={styles.container}>
            {loading ? (
                <ActivityIndicator size="large" color={Colors.primary} />
            ) : currentProfiles.length > 0 ? (
                <FlatList
                    data={currentProfiles}
                    renderItem={({item}) => (
                        <UserProfileCard
                            profile={item}
                            onLike={handleLikeProfile}
                            onRemove={handleRemoveProfile}
                        />
                    )}
                    keyExtractor={item => item.id}
                    extraData={currentProfiles}
                    horizontal
                    pagingEnabled
                    snapToAlignment="center"
                    snapToInterval={width}
                    decelerationRate="fast"
                    showsHorizontalScrollIndicator={false}
                    onEndReached={loadMoreProfiles}
                    onEndReachedThreshold={0.5}
                    ListFooterComponent={renderFooter}
                />
            ) : (
                <Text style={styles.noProfilesText}>
                    Tell others to join this Sootro group to see more profiles! Or wait for people to join.
                </Text>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: Colors.background,
    },
    leaveButton: {
        color: Colors.error,
        marginRight: 10,
        fontSize: 16,
        fontWeight: '600',
    },
    noProfilesText: {
        fontSize: 16,
        textAlign: 'center',
        marginTop: 20,
        width: '80%',
        alignSelf: 'center',
    },
    footerLoader: {
        width: Dimensions.get('window').width,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 10,
        color: Colors.textSecondary,
    },
});

export default Group;
