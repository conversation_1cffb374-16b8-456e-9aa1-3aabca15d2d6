import { useEffect, useState } from 'react';
import { Redirect, SplashScreen } from 'expo-router';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/config/firebaseConfig';
import { useSession } from '../ctx';
import appCheck from '@react-native-firebase/app-check';
import firebase from '@react-native-firebase/app';

// Prevent auto-hiding of splash screen
SplashScreen.preventAutoHideAsync();

type AppRoute = '/sign-in' | '/verify-email' | '/(app)';

export default function Index() {
  const { session } = useSession();
  const [appIsReady, setAppIsReady] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [route, setRoute] = useState<AppRoute | null>(null);

  useEffect(() => {
    async function prepareApp() {
      try {
        // ✅ Initialize App Check once here

        // --- Auth flow continues as before ---
        const authStatePromise = new Promise<User | null>((resolve) => {
          const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
            resolve(firebaseUser);
            unsubscribe();
          });
        });

        const firebaseUser = await authStatePromise;
        setUser(firebaseUser);

        if (!session || !firebaseUser) {
          setRoute('/sign-in');
        } else if (firebaseUser && !firebaseUser.emailVerified) {
          setRoute('/verify-email');
        } else {
          setRoute('/(app)');
        }
      } catch (e) {
        console.warn('Error preparing app:', e);
        setRoute('/sign-in');
      } finally {
        setAppIsReady(true);
        setTimeout(async () => {
          await SplashScreen.hideAsync();
        }, 100);
      }
    }

    prepareApp();
  }, [session]);

  if (!appIsReady || !route) {
    return null; // Keep showing splash
  }

  return <Redirect href={route} />;
}
