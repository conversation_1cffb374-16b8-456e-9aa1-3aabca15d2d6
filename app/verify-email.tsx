import React, {useEffect, useState} from 'react';
import {
    ActivityIndicator,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    Image,
} from 'react-native';
import {router} from 'expo-router';
import Icon from 'react-native-vector-icons/Ionicons';
import {getAuth, sendEmailVerification} from 'firebase/auth';
import Colors from '@/constants/Colors';

function VerifyEmail() {
    const [userEmail, setUserEmail] = useState('');
    const [status, setStatus] = useState(false);
    const auth = getAuth();

    useEffect(() => {
        const refreshUser = async () => {
            const currentUser = auth.currentUser;
            setUserEmail(currentUser?.email || '');
            if (currentUser) {
                await currentUser.reload();
                if (currentUser.emailVerified) {
                    setStatus(true);
                    setTimeout(() => router.replace('/onboarding'), 2000);
                    return;
                }
            }
        };

        const intervalId = setInterval(refreshUser, 5000);

        return () => clearInterval(intervalId);
    }, []);

    const logOut = () => {
        auth.signOut();
        router.replace('/sign-in');
    };

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.contentContainer}>
                <TouchableOpacity onPress={logOut} style={styles.logOutButton}>
                    <Icon name={'chevron-back'} size={24} color={Colors.primaryDark} />
                    <Text style={styles.logOutButtonText}>Back to Sign In</Text>
                </TouchableOpacity>
                
                <View style={styles.card}>
                    <View style={styles.iconContainer}>
                        <Icon 
                            name={'mail-outline'} 
                            size={50} 
                            color={Colors.primary} 
                            style={styles.emailIcon}
                        />
                    </View>
                    
                    <Text style={styles.headerText}>
                        Please verify your email
                    </Text>
                    
                    <Text style={styles.descriptionText}>
                        We've sent a verification link to
                    </Text>
                    
                    <Text style={styles.emailText}>{userEmail}</Text>
                    
                    <Text style={styles.instructionsText}>
                        Please check your inbox and click the verification link to continue
                    </Text>
                    
                    <View style={styles.statusContainer}>
                        {status ? (
                            <View style={styles.statusVerified}>
                                <Icon name={'checkmark-circle'} size={24} color={Colors.success} />
                                <Text style={styles.statusTextSuccess}>Verified successfully!</Text>
                            </View>
                        ) : (
                            <View style={styles.statusPending}>
                                <ActivityIndicator size="small" color={Colors.primary} />
                                <Text style={styles.statusTextPending}>Waiting for verification...</Text>
                            </View>
                        )}
                    </View>
                    
            
                </View>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    contentContainer: {
        flex: 1,
        padding: 20,
    },
    card: {
        backgroundColor: Colors.backgroundGray,
        borderRadius: 16,
        padding: 24,
        marginTop: 40,
        alignItems: 'center',
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
    },
    iconContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: Colors.primaryLight,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 24,
    },
    emailIcon: {
        opacity: 0.9,
    },
    headerText: {
        fontSize: 24,
        fontWeight: '700',
        color: Colors.text,
        textAlign: 'center',
        marginBottom: 16,
    },
    descriptionText: {
        fontSize: 16,
        color: Colors.textSecondary,
        textAlign: 'center',
        marginBottom: 8,
    },
    emailText: {
        fontSize: 16,
        fontWeight: '600',
        color: Colors.primary,
        textAlign: 'center',
        marginBottom: 16,
    },
    instructionsText: {
        fontSize: 14,
        color: Colors.textSecondary,
        textAlign: 'center',
        marginBottom: 24,
        lineHeight: 20,
    },
    statusContainer: {
        marginVertical: 16,
        alignItems: 'center',
    },
    statusVerified: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
    },
    statusPending: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(192, 155, 114, 0.1)',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 8,
    },
    statusTextSuccess: {
        marginLeft: 8,
        color: Colors.success,
        fontWeight: '600',
    },
    statusTextPending: {
        marginLeft: 8,
        color: Colors.primaryDark,
    },
    logOutButton: {
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'flex-start',
        padding: 6,
    },
    logOutButtonText: {
        fontSize: 16,
        color: Colors.primaryDark,
        fontWeight: '500',
        marginLeft: 4,
    },
    resendButton: {
        marginTop: 16,
        paddingVertical: 8,
    },
    resendButtonText: {
        color: Colors.primary,
        fontWeight: '500',
        fontSize: 14,
    },
});

export default VerifyEmail;
