import React, {useEffect, useRef, useState} from 'react';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    Modal,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    useWindowDimensions,
    View,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import {doc, getDoc, getFirestore, limit, setDoc} from 'firebase/firestore';
import {auth} from '@/config/firebaseConfig';
import {onAuthStateChanged, User} from 'firebase/auth';
import Toast from 'react-native-toast-message';
import {useRoute} from '@react-navigation/native';
import {router} from 'expo-router';
import {getFunctions, httpsCallable} from 'firebase/functions';
import {saveProfileImages} from '@/utils/imageUpload';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

interface Group {
    id: string;
    name: string;
    location: string;
    users: number;
    isMember?: boolean;
}

interface GroupsResponse {
    groups: Group[];
    hasMore: boolean;
    lastGroupId: string | null;
    lastGroupName: string | null;
}

const Onboarding = () => {
    const scrollRef = useRef<ScrollView>(null);
    const descriptionInputRef = useRef<TextInput>(null);
    const locationInputRef = useRef<TextInput>(null);
    const nameInputRef = useRef<TextInput>(null);
    const route = useRoute();
    const {userName: userNameParam} =
        (route.params as {userName?: string}) || {};
    const {width} = useWindowDimensions();
    const [screenHeight, setScreenHeight] = useState(
        Dimensions.get('window').height,
    );
    const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
    const [userName, setUserName] = useState(userNameParam || '');
    const [description, setDescription] = useState('');
    const [location, setLocation] = useState('');
    const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
    const [keywords, setKeywords] = useState<string[]>([]);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [isLoadingGroups, setIsLoadingGroups] = useState(false);
    const [suggestedGroups, setSuggestedGroups] = useState<any[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [locationQuery, setLocationQuery] = useState('');
    const [isSearching, setIsSearching] = useState(false);
    const [searchResults, setSearchResults] = useState<any[]>([]);
    const [isCreatingGroup, setIsCreatingGroup] = useState(false);
    const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
    const [newGroupName, setNewGroupName] = useState('');

    const [user, setUser] = useState<User | null>(null);
    const [savedImages, setSavedImages] = useState<string[]>([]);
    const [newImages, setNewImages] = useState<ImagePicker.ImagePickerAsset[]>(
        [],
    );
    const [imagesToDelete, setImagesToDelete] = useState<string[]>([]);
    const db = getFirestore();
    const functions = getFunctions();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [usedAIExtraction, setUsedAIExtraction] = useState<boolean>(false);

    useEffect(() => {
        const unsubscribe = onAuthStateChanged(auth, async currentUser => {
            if (currentUser) {
                setUser(currentUser);

                // Populate name from Google account if available
                if (currentUser.displayName && !userName) {
                    setUserName(currentUser.displayName);
                }

                await fetchProfileImages(currentUser.uid);

                // Also check if user name already exists in Firestore
                const userDoc = doc(db, 'users', currentUser.uid);
                const docSnap = await getDoc(userDoc);
                if (docSnap.exists()) {
                    const userData = docSnap.data();
                    if (userData.name && !userName) {
                        setUserName(userData.name);
                    }
                }
            } else {
                setUser(null);
            }
        });
        return () => unsubscribe();
    }, [userName]);

    const fetchSuggestedGroups = async (
        keywords: Array<string>,
        location: string,
    ) => {
        if (!user || keywords.length === 0) return;

        setIsLoadingGroups(true);
        
        try {
            // Call the searchGroups function directly but capture the results separately
            const searchGroupsCall = httpsCallable(functions, 'searchGroups');

            const result = await searchGroupsCall({
                query: keywords.slice(0, 5).join(' '),
                location: location.trim(),
                userId: user.uid,
                lastGroupId: null,
                lastGroupName: null,
                limit: 0,
            });

            const response = result.data as GroupsResponse;
            const groupsResult = response.groups || [];
            
            // Set suggested groups without affecting search results
            setSuggestedGroups(groupsResult.slice(0, 5));

            Toast.show({
                type: 'success',
                text1: 'Groups suggested based on your interests',
            });
        } catch (error) {
            console.error('Error fetching suggested groups:', error);
            Toast.show({
                type: 'error',
                text1: 'Error fetching suggested groups',
                text2: 'Please try again',
            });
        } finally {
            setIsLoadingGroups(false);
        }
    };

    const searchGroups = async (query: string, location: string) => {
        let queryArray: string[] = [];
        if (!user || (!query.trim() && !location.trim())) return;

        setIsSearching(true);

        try {
            // Call the searchGroups function with the search query and location
            const searchGroupsCall = httpsCallable(functions, 'searchGroups');

            console.log('Searching with params:', {
                query,
                location,
                userId: user.uid,
            });

            const result = await searchGroupsCall({
                query: query,
                location: location.trim(),
                userId: user.uid,
                lastGroupId: null,
                lastGroupName: null,
                limit: 0,
            });

            // Update search results - searchGroups returns {groups: [], hasMore, ...}
            const response = result.data as GroupsResponse;
            const groupsResult = response.groups || [];
            console.log('Search results:', groupsResult);
            console.log('Setting search results with length:', groupsResult.length);
            setSearchResults(groupsResult);

        } catch (error) {
            console.error('Error searching groups:', error);
            Toast.show({
                type: 'error',
                text1: 'Error searching groups',
                text2: 'Please try again',
            });
        } finally {
            setIsSearching(false);
        }
    };

    const handleSearchSubmit = () => {
        if (searchQuery.trim() || locationQuery.trim()) {
            searchGroups(searchQuery, locationQuery);
        }
    };

    // Clear search results when the search query changes
    useEffect(() => {
        if (!searchQuery.trim() && !locationQuery.trim()) {
            setSearchResults([]);
        }
    }, [searchQuery, locationQuery]);

    // Debug searchResults changes
    useEffect(() => {
        console.log('searchResults state changed:', searchResults.length, searchResults);
    }, [searchResults]);

    const handleBack = () => {
        if (currentSlideIndex > 0) {
            const prevIndex = currentSlideIndex - 1;
            scrollRef.current?.scrollTo({x: prevIndex * width, animated: true});
            setCurrentSlideIndex(prevIndex);
        }
    };

    const fetchProfileImages = async (uid: string) => {
        const userDoc = doc(db, 'users', uid);
        const docSnap = await getDoc(userDoc);
        if (docSnap.exists()) {
            const data = docSnap.data();
            setSavedImages(data.images || []);
        }
    };

    const saveName = async () => {
        if (!user) return;
        const userDocRef = doc(db, 'users', user.uid);
        await setDoc(userDocRef, {name: userName}, {merge: true});
        Toast.show({
            type: 'success',
            text1: 'Name saved',
        });
    };

    const saveDescription = async () => {
        if (!user) return;
        setIsAnalyzing(true);

        try {
            // Call the Firebase function to analyze description and extract keywords
            const analyzeDescriptionCall = httpsCallable(
                functions,
                'analyzeDescription',
            );
            console.log('Analyzing description:', description);

            const result = await analyzeDescriptionCall({
                description: description,
            });

            console.log('Analyze description result:', result.data);

            // Update local state with the keywords extracted
            if (result.data && (result.data as any).success) {
                const extractedKeywords = (result.data as any).keywords || [];
                const usedFallback = (result.data as any).usedFallback || false;

                console.log('Extracted keywords:', extractedKeywords);
                console.log('Used fallback method:', usedFallback);

                setKeywords(extractedKeywords);
                setUsedAIExtraction(!usedFallback);

                // Also save description and keywords to Firestore
                const userDocRef = doc(db, 'users', user.uid);
                await setDoc(
                    userDocRef,
                    {
                        description,
                        location,
                        keywords: extractedKeywords,
                    },
                    {merge: true},
                );

                // Fetch suggested groups based on keywords
                if (extractedKeywords.length > 0) {
                    // Add a slight delay to ensure state is updated
                    setTimeout(() => {
                        fetchSuggestedGroups(extractedKeywords, location);
                    }, 500);
                }
            }

            Toast.show({
                type: 'success',
                text1: 'Description analyzed',
                text2: 'We found some keywords that match your interests!',
            });
        } catch (_) {
            Toast.show({
                type: 'error',
                text1: 'Error analyzing description',
                text2: 'Failed to analyze description',
            });
        } finally {
            setIsAnalyzing(false);
        }
    };

    const saveGroups = async () => {
        if (!user || selectedGroups.length === 0) return;

        try {
            // Use joinGroup function to save selected groups to the user's profile
            const joinGroupFunction = httpsCallable(functions, 'joinGroup');

            // Choose the first group to join, and include all selected groups
            const firstGroupId = selectedGroups[0];
            await joinGroupFunction({
                userId: user.uid,
                groupId: firstGroupId,
                selectedGroups: selectedGroups,
            });

            Toast.show({
                type: 'success',
                text1: 'Groups saved',
            });
        } catch (error) {
            console.error('Error saving groups:', error);
            Toast.show({
                type: 'error',
                text1: 'Error saving groups',
            });
        }
    };

    const createGroup = async () => {
        if (!user || !newGroupName.trim()) return;

        setIsCreatingGroup(true);

        try {
            const createGroupFunction = httpsCallable(functions, 'createGroup');
            const result = await createGroupFunction({
                userId: user.uid,
                groupName: newGroupName.trim(),
                location: locationQuery.trim() || location.trim(),
            });

            const groupData = result.data as any;
            
            if (groupData.success) {
                // Add the newly created group to selected groups
                setSelectedGroups([groupData.groupId]);
                
                // Show the created group in search results
                const newGroup = {
                    id: groupData.groupId,
                    name: groupData.groupName,
                    location: locationQuery.trim() || location.trim() || 'Unknown Location',
                    users: 1, // Creator is the first member
                    isMember: true,
                };
                
                setSearchResults([newGroup]);
                setSuggestedGroups([newGroup]);
                
                Toast.show({
                    type: 'success',
                    text1: 'Group created successfully!',
                    text2: `"${groupData.groupName}" is ready to go`,
                });

                // Close the modal and clear the input
                setShowCreateGroupModal(false);
                setNewGroupName('');
            }
        } catch (error) {
            console.error('Error creating group:', error);
            Toast.show({
                type: 'error',
                text1: 'Error creating group',
                text2: 'Please try again',
            });
        } finally {
            setIsCreatingGroup(false);
        }
    };

    const handlePickImage = async () => {
        const remainingSlots =
            6 - (savedImages.length + newImages.length - imagesToDelete.length);
        if (remainingSlots <= 0) return;

        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsMultipleSelection: true,
            selectionLimit: remainingSlots,
            quality: 1,
        });

        if (!result.canceled && result.assets) {
            setNewImages(prev => [...prev, ...result.assets]);
        }
    };

    const handleDeleteImage = (
        image: string | ImagePicker.ImagePickerAsset,
    ) => {
        const totalImages =
            savedImages.length + newImages.length - imagesToDelete.length;
        if (totalImages <= 1) {
            Toast.show({
                type: 'error',
                text1: "Can't delete the only image",
                text2: 'Please upload another image before deleting this one.',
            });
            return;
        }
        if (typeof image === 'string') {
            setImagesToDelete(prev => [...prev, image]);
        } else {
            setNewImages(prev => prev.filter(img => img.uri !== image.uri));
        }
    };

    const handleSaveProfileImages = async () => {
        if (!user) {
            console.error('No user found when saving profile images');
            Toast.show({
                type: 'error',
                text1: 'Authentication error',
                text2: 'Please try signing in again',
            });
            return;
        }

        const totalImages =
            savedImages.length + newImages.length - imagesToDelete.length;
        if (totalImages === 0) {
            Toast.show({
                type: 'error',
                text1: 'No images',
                text2: 'At least one image is required.',
            });
            return;
        }

        // Set loading state to true
        setIsSubmitting(true);

        // Use the shared utility function
        const result = await saveProfileImages({
            db,
            userId: user.uid,
            savedImages,
            newImages,
            imagesToDelete,
            setIsSubmitting: (isSubmitting: boolean) =>
                setIsSubmitting(isSubmitting),
            onSuccess: (finalImages: string[]) => {
                // Update local state
                setSavedImages(finalImages);
                setNewImages([]);
                setImagesToDelete([]);

                // Small delay to ensure Firestore write has propagated
                setTimeout(() => {
                    try {
                        console.log('Navigating to home screen');
                        router.replace('/');
                    } catch (navError) {
                        console.error('Navigation error:', navError);
                        setIsSubmitting(false);

                        // If navigation fails, show error
                        Toast.show({
                            type: 'error',
                            text1: 'Navigation error',
                            text2: 'Please try again',
                        });
                    }
                }, 1000);
            },
            onError: (error: Error) => {
                console.error('Error in handleSaveProfileImages:', error);
                setIsSubmitting(false);
            },
            additionalData: {}, // No additional data needed for onboarding
        });
    };

    const handleNext = () => {
        const nextIndex = currentSlideIndex + 1;

        // Make sure you don't go past the last slide
        if (nextIndex < 4) {
            scrollRef.current?.scrollTo({x: nextIndex * width, animated: true});
            setCurrentSlideIndex(nextIndex);
        }
    };

    const toggleGroup = (groupId: string) => {
        setSelectedGroups(prev => {
            if (prev.includes(groupId)) {
                return prev.filter(id => id !== groupId);
            }
            return [...prev, groupId];
        });
    };

    // Set up focus changes when slide index changes
    useEffect(() => {
        if (currentSlideIndex === 0) {
            // Focus the name input when on the first slide
            setTimeout(() => nameInputRef.current?.focus(), 500);
        } else if (currentSlideIndex === 1) {
            // Focus the description input when on the second slide
            setTimeout(() => descriptionInputRef.current?.focus(), 500);
        }
    }, [currentSlideIndex]);

    // Update dimensions when screen size changes (e.g., rotation)
    useEffect(() => {
        const updateDimensions = () => {
            setScreenHeight(Dimensions.get('window').height);
        };

        const dimensionsListener = Dimensions.addEventListener(
            'change',
            updateDimensions,
        );

        return () => {
            dimensionsListener.remove();
        };
    }, []);

    // Calculate content heights based on device dimensions
    const getContentHeights = () => {
        // Adjust values as needed for your design
        const statusBarHeight = Platform.OS === 'ios' ? 44 : 24;
        const headerHeight = 60;
        const searchSectionHeight = 120;
        const toolbarHeight = 70;
        const bottomSafeArea = Platform.OS === 'ios' ? 80 : 0;

        return {
            mainContent:
                screenHeight -
                statusBarHeight -
                headerHeight -
                searchSectionHeight -
                toolbarHeight -
                bottomSafeArea,
            groupsScroll: screenHeight,
            toolbarBottom: Platform.OS === 'ios' ? bottomSafeArea : 0,
        };
    };

    const heights = getContentHeights();

    return (
        <KeyboardAwareScrollView
            enableOnAndroid={true}
            enableAutomaticScroll={Platform.OS === 'ios'}
            keyboardShouldPersistTaps="handled"
            extraScrollHeight={20}
            style={[styles.container, {height: screenHeight}]}
            contentContainerStyle={styles.containerContent}>
            {currentSlideIndex > 0 && (
                <TouchableOpacity
                    style={styles.backButton}
                    onPress={handleBack}>
                    <Text style={styles.backButtonText}>← Back</Text>
                </TouchableOpacity>
            )}

            {/* Progress indicator */}
            <View style={styles.progressContainer}>
                {[0, 1, 2, 3].map(index => (
                    <View
                        key={index}
                        style={[
                            styles.progressDot,
                            currentSlideIndex >= index
                                ? styles.progressDotActive
                                : {},
                        ]}
                    />
                ))}
            </View>

            <ScrollView
                ref={scrollRef}
                horizontal
                pagingEnabled
                scrollEnabled={false}
                style={styles.container}
                showsHorizontalScrollIndicator={false}>
                {/* Slide 1: Ask for name */}
                <View style={[styles.slide, {width}]}>
                    <Text style={styles.title} className="mb-10">
                        Let's Jump In to Get Started
                    </Text>
                    <Text style={styles.subtitle}>What is your name?</Text>

                    {user &&
                    user.displayName &&
                    userName === user.displayName ? (
                        <View style={styles.googleNameContainer}>
                            <Text style={styles.googleNameText}>
                                We've populated your name from Google:{' '}
                                {userName}
                            </Text>
                            <Text style={styles.googleNameSubtext}>
                                You can change it if you'd like
                            </Text>
                        </View>
                    ) : null}

                    <TextInput
                        style={styles.input}
                        value={userName}
                        onChangeText={setUserName}
                        placeholder="Enter your name"
                        placeholderTextColor="#AAAAAA"
                        autoComplete="name"
                        ref={nameInputRef}
                    />

                    <TouchableOpacity
                        style={[
                            styles.actionButton,
                            !userName.trim() ? styles.actionButtonDisabled : {},
                        ]}
                        onPress={() => {
                            saveName();
                            handleNext();
                        }}
                        disabled={!userName.trim()}>
                        <Text style={styles.actionButtonText}>Next</Text>
                    </TouchableOpacity>
                </View>

                {/* Slide 2: Description */}
                <View style={[styles.slide, {width}]}>
                    <Text style={styles.title} className="mb-10">
                        Spill the Tea
                    </Text>
                    <Text style={styles.subtitle}>
                        Tell us a bit about your interests or who you are.
                    </Text>
                    <TextInput
                        multiline
                        style={[styles.input, styles.textArea]}
                        value={description}
                        onChangeText={setDescription}
                        placeholder="Share a short description"
                        placeholderTextColor="#AAAAAA"
                        textAlignVertical="top"
                        autoComplete="off"
                        ref={descriptionInputRef}
                    />
                    <TextInput
                        style={styles.input}
                        value={location}
                        onChangeText={setLocation}
                        placeholder="City"
                        placeholderTextColor="#AAAAAA"
                        autoComplete="postal-address-locality"
                        ref={locationInputRef}
                    />

                    <TouchableOpacity
                        style={[
                            styles.actionButton,
                            !description.trim() || isAnalyzing
                                ? styles.actionButtonDisabled
                                : {},
                        ]}
                        onPress={() => {
                            saveDescription();
                            setTimeout(() => {
                                handleNext();
                            }, 1000);
                        }}
                        disabled={!description.trim() || isAnalyzing}>
                        <Text style={styles.actionButtonText}>
                            {isAnalyzing ? 'Analyzing...' : 'Next'}
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Slide 3: Group selection */}
                <View style={[styles.slide, {width, position: 'relative'}]}>
                    <Text style={styles.title} className="mb-5">
                        Find Your People
                    </Text>

                    {/* Search fields */}
                    <View style={styles.searchSection}>
                        {/* Group name search field */}
                        <View style={styles.searchContainer}>
                            <View style={styles.inputIconContainer}>
                                <TextInput
                                    style={styles.searchInput}
                                    value={searchQuery}
                                    onChangeText={setSearchQuery}
                                    placeholder="Search by group name..."
                                    returnKeyType="search"
                                    onSubmitEditing={handleSearchSubmit}
                                    placeholderTextColor="#AAAAAA"
                                    className="me-2"
                                />
                            </View>
                            <TouchableOpacity
                                style={[
                                    styles.searchButton,
                                    isSearching
                                        ? styles.searchButtonDisabled
                                        : {},
                                ]}
                                onPress={handleSearchSubmit}
                                disabled={isSearching}>
                                <Text style={styles.searchButtonText}>
                                    {isSearching ? 'Searching...' : 'Search'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Main content area with padding for the fixed toolbar */}
                    <View
                        style={[
                            styles.groupsContentArea,
                            {minHeight: heights.mainContent},
                        ]}>
                        {/* Display search results if any */}
                        {(searchQuery.trim() || locationQuery.trim()) && (
                            <View>
                                {isSearching ? (
                                    <View style={styles.loadingContainer}>
                                        <ActivityIndicator
                                            size="large"
                                            color="#C09B72"
                                        />
                                        <Text style={styles.loadingText}>
                                            Searching for groups...
                                        </Text>
                                    </View>
                                ) : searchResults.length > 0 ? (
                                    <>
                                        <View style={styles.sectionHeader}>
                                            <Text style={styles.sectionTitle}>
                                                Search Results
                                            </Text>
                                            <Text style={styles.sectionCount}>
                                                {searchResults.length} found
                                            </Text>
                                        </View>
                                        <ScrollView
                                            style={[
                                                styles.groupsContainer,
                                                {
                                                    maxHeight:
                                                        heights.groupsScroll,
                                                },
                                            ]}>
                                            {searchResults.map(group => (
                                                <TouchableOpacity
                                                    key={group.id}
                                                    style={[
                                                        styles.groupItem,
                                                        selectedGroups.includes(
                                                            group.id,
                                                        ) &&
                                                            styles.groupItemSelected,
                                                    ]}
                                                    onPress={() =>
                                                        toggleGroup(group.id)
                                                    }>
                                                    <View
                                                        style={
                                                            styles.groupContent
                                                        }>
                                                        <View
                                                            style={
                                                                styles.groupHeader
                                                            }>
                                                            <Text
                                                                style={
                                                                    styles.groupName
                                                                }>
                                                                {group.name}
                                                            </Text>
                                                            {selectedGroups.includes(
                                                                group.id,
                                                            ) && (
                                                                <View
                                                                    style={
                                                                        styles.checkmarkCircle
                                                                    }>
                                                                    <Text
                                                                        style={
                                                                            styles.checkmark
                                                                        }>
                                                                        ✓
                                                                    </Text>
                                                                </View>
                                                            )}
                                                        </View>
                                                        {group.location && group.location.trim() !== '' && (
                                                            <View
                                                                style={
                                                                    styles.groupInfoRow
                                                                }>
                                                                <Text
                                                                    style={
                                                                        styles.groupInfoLabel
                                                                    }>
                                                                    📍
                                                                </Text>
                                                                <Text
                                                                    style={[
                                                                        styles.groupLocation,
                                                                        group.matchedByLocation
                                                                            ? styles.matchedLocation
                                                                            : {},
                                                                    ]}>
                                                                    {group.location}
                                                                    {group.exactLocationMatch ? (
                                                                    <Text
                                                                        style={
                                                                            styles.matchBadge
                                                                        }>
                                                                        {' '}
                                                                        (exact
                                                                        match)
                                                                    </Text>
                                                                ) : group.matchedByLocation ? (
                                                                    <Text
                                                                        style={
                                                                            styles.matchBadge
                                                                        }>
                                                                        {' '}
                                                                        (matched)
                                                                    </Text>
                                                                ) : null}
                                                            </Text>
                                                        </View>
                                                        )}
                                                    </View>
                                                </TouchableOpacity>
                                            ))}
                                        </ScrollView>
                                    </>
                                ) : (
                                    <Text style={styles.noResultsText}>
                                        No groups found for your search
                                        criteria. Don't worry, you can create
                                        groups in Sootro.
                                    </Text>
                                )}
                            </View>
                        )}

                        {/* Conditionally display suggested groups */}
                        {!searchQuery.trim() && !locationQuery.trim() && (
                            <>
                                {isLoadingGroups ? (
                                    <View style={styles.loadingContainer}>
                                        <ActivityIndicator
                                            size="large"
                                            color="#C09B72"
                                        />
                                        <Text style={styles.loadingText}>
                                            Finding groups for you...
                                        </Text>
                                    </View>
                                ) : suggestedGroups.length > 0 ? (
                                    <>
                                        <View style={styles.sectionHeader}>
                                            <Text style={styles.sectionCount}>
                                                {suggestedGroups.length} groups
                                            </Text>
                                        </View>
                                        <ScrollView
                                            style={[
                                                styles.groupsContainer,
                                                {
                                                    maxHeight:
                                                        heights.groupsScroll,
                                                },
                                            ]}>
                                            {suggestedGroups.map(group => (
                                                <TouchableOpacity
                                                    key={group.id}
                                                    style={[
                                                        styles.groupItem,
                                                        selectedGroups.includes(
                                                            group.id,
                                                        ) &&
                                                            styles.groupItemSelected,
                                                    ]}
                                                    onPress={() =>
                                                        toggleGroup(group.id)
                                                    }>
                                                    <View
                                                        style={
                                                            styles.groupContent
                                                        }>
                                                        <View
                                                            style={
                                                                styles.groupHeader
                                                            }>
                                                            <Text
                                                                style={
                                                                    styles.groupName
                                                                }>
                                                                {group.name}
                                                            </Text>
                                                            {selectedGroups.includes(
                                                                group.id,
                                                            ) && (
                                                                <View
                                                                    style={
                                                                        styles.checkmarkCircle
                                                                    }>
                                                                    <Text
                                                                        style={
                                                                            styles.checkmark
                                                                        }>
                                                                        ✓
                                                                    </Text>
                                                                </View>
                                                            )}
                                                        </View>
                                                        {group.location && group.location.trim() !== '' && (
                                                            <View
                                                                style={
                                                                    styles.groupInfoRow
                                                                }>
                                                                <Text
                                                                    style={
                                                                        styles.groupInfoLabel
                                                                    }>
                                                                    📍
                                                                </Text>
                                                                <Text
                                                                    style={
                                                                        styles.groupLocation
                                                                    }>
                                                                    {group.location}
                                                                </Text>
                                                            </View>
                                                        )}
                                                    </View>
                                                </TouchableOpacity>
                                            ))}

                                            <View
                                                style={
                                                    styles.createGroupInfoContainer
                                                }>
                                                <Text
                                                    style={
                                                        styles.createGroupInfoIcon
                                                    }>
                                                    ✨
                                                </Text>
                                                <Text
                                                    style={
                                                        styles.createGroupInfoText
                                                    }>
                                                    Don't worry, you can create
                                                    groups in Sootro
                                                </Text>
                                            </View>
                                        </ScrollView>
                                    </>
                                ) : (
                                    <View style={styles.noGroupsContainerBox}>
                                        <Text style={styles.noGroupsIcon}>
                                            🔍
                                        </Text>
                                        <Text style={styles.noGroupsTitle}>
                                            No Groups Found
                                        </Text>
                                        <Text
                                            style={styles.noGroupsTextContent}>
                                            No groups match your search. Would you like to create one?
                                        </Text>
                                        
                                        <TouchableOpacity 
                                            style={styles.createGroupButton}
                                            onPress={() => {
                                                setNewGroupName(searchQuery || '');
                                                setShowCreateGroupModal(true);
                                            }}
                                        >
                                            <Text style={styles.createGroupButtonText}>
                                                ✨ Create "{searchQuery || 'New Group'}"
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                )}
                            </>
                        )}
                    </View>

                    {/* Fixed toolbar at the bottom */}
                    <View
                        style={{
                            position: 'absolute',
                            bottom: 120,
                            left: 0,
                            right: 0,
                            width: width,
                            height: Platform.OS === 'ios' ? 104 : 90, // Include safe area
                            backgroundColor: '#FFFFFF',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingHorizontal: 20,
                            paddingBottom: Platform.OS === 'ios' ? 34 : 20,
                            borderTopWidth: 1,
                            borderTopColor: '#E8E8E8',
                            shadowColor: '#000',
                            shadowOffset: {width: 0, height: -2},
                            shadowOpacity: 0.08,
                            shadowRadius: 3,
                            elevation: 4,
                            zIndex: 999,
                        }}>
                        <View style={styles.selectedGroupsInfo}>
                            <Text style={styles.selectedGroupsText}>
                                {selectedGroups.length}{' '}
                                {selectedGroups.length === 1
                                    ? 'group'
                                    : 'groups'}{' '}
                                selected
                            </Text>
                        </View>
                        <TouchableOpacity
                            style={[
                                styles.toolbarButton,
                                isLoadingGroups || isSearching
                                    ? styles.toolbarButtonDisabled
                                    : {},
                            ]}
                            onPress={() => {
                                saveGroups();
                                handleNext();
                            }}
                            disabled={isLoadingGroups || isSearching}>
                            <Text style={styles.toolbarButtonText}>
                                Continue
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Slide 4: Photo Upload */}
                <View style={[styles.slide, {width}]}>
                    <Text style={styles.titleNarrow} className="mb-10">
                        Let's Put a Face to the Name
                    </Text>
                    <Text style={styles.subtitle}>
                        Add photos that represent you. You can change them
                        anytime.
                    </Text>

                    <View style={styles.imageHelpText}>
                        <Text style={styles.imageCounter}>
                            {savedImages.length +
                                newImages.length -
                                imagesToDelete.length}
                            /6 photos added
                        </Text>
                        <Text style={styles.imageTip}>
                            Tap a photo to remove it
                        </Text>
                    </View>

                    <View style={styles.imageGrid}>
                        {savedImages
                            .filter(img => !imagesToDelete.includes(img))
                            .map((image, index) => (
                                <TouchableOpacity
                                    key={`saved-${index}`}
                                    onPress={() => handleDeleteImage(image)}
                                    style={styles.imageWrapper}>
                                    <Image
                                        source={{uri: image}}
                                        style={styles.image}
                                    />
                                    <View style={styles.imageOverlay}>
                                        <Text style={styles.deleteImageIcon}>
                                            ✕
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            ))}
                        {newImages.map((image, index) => (
                            <TouchableOpacity
                                key={`new-${index}`}
                                onPress={() => handleDeleteImage(image)}
                                style={styles.imageWrapper}>
                                <Image
                                    source={{uri: image.uri}}
                                    style={styles.image}
                                />
                                <View style={styles.imageOverlay}>
                                    <Text style={styles.deleteImageIcon}>
                                        ✕
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        ))}
                        {savedImages.length +
                            newImages.length -
                            imagesToDelete.length <
                            6 && (
                            <TouchableOpacity
                                onPress={handlePickImage}
                                style={[styles.imageWrapper, styles.addImage]}>
                                <Text style={styles.addImageText}>+</Text>
                                <Text style={styles.addImageLabel}>
                                    Add Photo
                                </Text>
                            </TouchableOpacity>
                        )}
                    </View>

                    <View style={styles.finishButtonContainer}>
                        <TouchableOpacity
                            style={[
                                styles.finishButton,
                                savedImages.length +
                                    newImages.length -
                                    imagesToDelete.length ===
                                    0 || isSubmitting
                                    ? styles.actionButtonDisabled
                                    : {},
                            ]}
                            onPress={handleSaveProfileImages}
                            disabled={
                                savedImages.length +
                                    newImages.length -
                                    imagesToDelete.length ===
                                    0 || isSubmitting
                            }>
                            {isSubmitting ? (
                                <View style={styles.loadingButtonContent}>
                                    <ActivityIndicator
                                        size="small"
                                        color="#FFFFFF"
                                    />
                                    <Text
                                        style={[
                                            styles.finishButtonText,
                                            styles.loadingButtonText,
                                        ]}>
                                        Saving...
                                    </Text>
                                </View>
                            ) : (
                                <Text style={styles.finishButtonText}>
                                    Finish & Start Using Sootro
                                </Text>
                            )}
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>

            {/* Create Group Modal */}
            <Modal
                visible={showCreateGroupModal}
                animationType="slide"
                transparent={true}
                onRequestClose={() => setShowCreateGroupModal(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>Create New Group</Text>
                        <Text style={styles.modalSubtitle}>
                            What would you like to call your group?
                        </Text>
                        
                        <TextInput
                            style={styles.modalInput}
                            value={newGroupName}
                            onChangeText={setNewGroupName}
                            placeholder={searchQuery || "Enter group name"}
                            placeholderTextColor="#AAAAAA"
                            autoFocus={true}
                        />
                        
                        <View style={styles.modalButtons}>
                            <TouchableOpacity
                                style={[styles.modalButton, styles.modalCancelButton]}
                                onPress={() => {
                                    setShowCreateGroupModal(false);
                                    setNewGroupName('');
                                }}
                            >
                                <Text style={styles.modalCancelButtonText}>Cancel</Text>
                            </TouchableOpacity>
                            
                            <TouchableOpacity
                                style={[
                                    styles.modalButton, 
                                    styles.modalCreateButton,
                                    (!newGroupName.trim() || isCreatingGroup) && styles.modalButtonDisabled
                                ]}
                                onPress={createGroup}
                                disabled={!newGroupName.trim() || isCreatingGroup}
                            >
                                {isCreatingGroup ? (
                                    <ActivityIndicator size="small" color="#FFFFFF" />
                                ) : (
                                    <Text style={styles.modalCreateButtonText}>Create Group</Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </KeyboardAwareScrollView>
    );
};

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        backgroundColor: '#FFF',
    },
    container: {
        flex: 1,
        backgroundColor: '#FFF',
        minHeight: 500,
        paddingTop: 40
    },
    containerContent: {
        flexGrow: 1,
    },
    slide: {
        padding: 10,
        paddingTop: Platform.OS === 'ios' ? 30 : 50,
        paddingBottom: 0,
        justifyContent: 'flex-start',
        backgroundColor: '#FFFFFF',
        height: Dimensions.get('window').height - 30, // Account for padding and status bar
        position: 'relative',
    },
    title: {
        fontWeight: '700',
        fontSize: 32,
        width: '90%',
        margin: 'auto',
        marginBottom: 16,
        marginTop: 20,
        textAlign: 'center',
        color: '#333333',
        letterSpacing: 0.3,
    },
    subtitle: {
        fontSize: 18,
        color: '#666666',
        marginBottom: 24,
        textAlign: 'center',
        width: '85%',
        margin: 'auto',
        marginTop: 0,
        lineHeight: 24,
    },
    titleNarrow: {
        fontWeight: '700',
        fontSize: 32,
        width: '85%',
        textAlign: 'center',
        margin: 'auto',
        marginBottom: 24,
        marginTop: 0,
        color: '#333333',
        letterSpacing: 0.3,
    },
    input: {
        borderWidth: 1,
        borderColor: '#D8D8D8',
        borderRadius: 12,
        marginVertical: 12,
        padding: 16,
        height: 54,
        width: '100%',
        fontSize: 16,
        backgroundColor: '#F8F8F8',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
    },
    groupsContainer: {
        maxHeight: 380,
        marginBottom: 10,
    },
    groupItem: {
        padding: 16,
        borderWidth: 1,
        borderColor: '#E8E8E8',
        borderRadius: 12,
        marginBottom: 12,
        backgroundColor: '#FFFFFF',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.05,
        shadowRadius: 3,
        elevation: 2,
    },
    groupItemSelected: {
        borderColor: '#C09B72',
        backgroundColor: '#FBF7F2',
        shadowColor: '#C09B72',
        shadowOpacity: 0.2,
    },
    groupContent: {
        flex: 1,
    },
    groupHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    groupName: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333333',
    },
    groupCheck: {
        fontSize: 18,
        color: '#C09B72',
        fontWeight: 'bold',
    },
    groupLocation: {
        fontSize: 15,
        color: '#666666',
        marginBottom: 4,
    },
    groupMembers: {
        fontSize: 14,
        color: '#888888',
    },
    noGroupsContainerBox: {
        padding: 24,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#F8F8F8',
        borderRadius: 12,
        marginVertical: 20,
    },
    noGroupsTextContent: {
        textAlign: 'center',
        color: '#666666',
        fontSize: 15,
        lineHeight: 22,
        marginBottom: 5,
    },
    imageGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    imageWrapper: {
        width: '30%',
        aspectRatio: 1,
        marginBottom: '5%',
        borderWidth: 1,
        borderColor: '#E0E0E0',
        borderRadius: 12,
        overflow: 'hidden',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#F8F8F8',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
    },
    image: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    addImage: {
        backgroundColor: '#F1EBE4',
        borderStyle: 'dashed',
        borderColor: '#C09B72',
    },
    addImageText: {
        fontSize: 32,
        color: '#C09B72',
        fontWeight: '300',
    },
    loadingContainer: {
        marginVertical: 20,
        alignItems: 'center',
        backgroundColor: '#F8F8F8',
        padding: 16,
        borderRadius: 12,
    },
    loadingText: {
        marginTop: 12,
        color: '#666666',
        fontSize: 14,
    },
    keywordsContainer: {
        marginVertical: 16,
        backgroundColor: '#F8F8F8',
        padding: 2,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#E8E8E8',
    },
    keywordsHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 10,
    },
    keywordsTitle: {
        fontWeight: '600',
        marginBottom: 10,
        color: '#333333',
        fontSize: 15,
    },
    keywordsList: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    keywordItem: {
        backgroundColor: '#E9E1D8',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 20,
        margin: 4,
    },
    keywordText: {
        fontSize: 13,
        color: '#8A6E52',
    },
    searchSection: {
        marginVertical: 16,
        width: '100%',
    },
    searchLabel: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 12,
        color: '#333333',
    },
    searchContainer: {
        flexDirection: 'row',
    },
    searchContainerWithButton: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    searchInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#D8D8D8',
        borderRadius: 10,
        padding: 12,
        backgroundColor: '#F8F8F8',
        fontSize: 15,
    },
    searchInputWithButton: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#D8D8D8',
        borderRadius: 10,
        padding: 12,
        marginRight: 10,
        backgroundColor: '#F8F8F8',
        fontSize: 15,
    },
    searchButton: {
        backgroundColor: '#C09B72',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 10,
        justifyContent: 'center',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
    },
    searchButtonDisabled: {
        backgroundColor: '#E9E1D8',
    },
    searchButtonText: {
        color: 'white',
        fontWeight: '600',
        fontSize: 15,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginVertical: 12,
        color: '#333333',
    },
    sectionCount: {
        fontSize: 14,
        color: '#888888',
    },
    sectionHeader: {
        marginBottom: 12,
    },
    noResultsText: {
        textAlign: 'center',
        color: '#666666',
        marginVertical: 16,
        backgroundColor: '#F8F8F8',
        padding: 16,
        borderRadius: 12,
        fontSize: 15,
    },
    googleNameContainer: {
        backgroundColor: '#F1EBE4',
        padding: 16,
        borderRadius: 12,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: '#E9E1D8',
    },
    googleNameText: {
        fontSize: 15,
        color: '#333333',
    },
    googleNameSubtext: {
        fontSize: 13,
        color: '#888888',
        marginTop: 6,
    },
    matchedLocation: {
        color: '#C09B72',
        fontWeight: '500',
    },
    matchBadge: {
        color: '#C09B72',
        fontSize: 12,
        fontStyle: 'italic',
    },
    groupsContentArea: {
        flex: 1,
        paddingBottom: Platform.OS === 'android' ? 110 : 120,
    },
    selectedGroupsInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    selectedGroupsText: {
        fontSize: 15,
        color: '#666666',
        fontWeight: '500',
    },
    toolbarButton: {
        backgroundColor: '#C09B72',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
    },
    toolbarButtonDisabled: {
        backgroundColor: '#E9E1D8',
    },
    toolbarButtonText: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: 16,
    },
    backButton: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 58 : 38,
        left: 20,
        zIndex: 999,
        paddingVertical: 8,
        paddingHorizontal: 12,
        borderRadius: 20,
        backgroundColor: '#F1EBE4',
    },
    backButtonText: {
        color: '#8A6E52',
        fontWeight: '500',
        fontSize: 14,
    },
    progressContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: Platform.OS === 'ios' ? 50 : 30,
        marginBottom: 10,
        zIndex: 10,
    },
    progressDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#E0E0E0',
        marginHorizontal: 4,
    },
    progressDotActive: {
        backgroundColor: '#C09B72',
        width: 16,
        height: 8,
    },
    actionButton: {
        backgroundColor: '#C09B72',
        paddingVertical: 16,
        paddingHorizontal: 20,
        borderRadius: 12,
        alignItems: 'center',
        marginTop: 20,
        marginBottom: 30,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
    },
    actionButtonDisabled: {
        backgroundColor: '#E9E1D8',
        shadowOpacity: 0,
    },
    actionButtonText: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: 16,
    },
    textArea: {
        height: 120,
        textAlignVertical: 'top',
        paddingTop: 12,
    },
    inputIconContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    inputIcon: {
        fontSize: 16,
        paddingLeft: 12,
        paddingRight: 4,
    },
    checkmarkCircle: {
        width: 26,
        height: 26,
        borderRadius: 13,
        backgroundColor: '#C09B72',
        alignItems: 'center',
        justifyContent: 'center',
    },
    checkmark: {
        color: '#FFFFFF',
        fontSize: 14,
        fontWeight: 'bold',
    },
    groupInfoRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    groupInfoLabel: {
        fontSize: 14,
        marginRight: 6,
    },
    noGroupsIcon: {
        fontSize: 32,
        marginBottom: 12,
    },
    noGroupsTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 8,
        color: '#666666',
    },
    imageHelpText: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    imageCounter: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '500',
    },
    imageTip: {
        fontSize: 14,
        color: '#888888',
        fontStyle: 'italic',
    },
    imageOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
        opacity: 0,
    },
    deleteImageIcon: {
        color: '#FFFFFF',
        fontSize: 24,
        fontWeight: '300',
    },
    addImageLabel: {
        color: '#8A6E52',
        fontSize: 12,
        marginTop: 6,
        fontWeight: '500',
    },
    finishButtonContainer: {
        marginTop: 20,
        marginBottom: 20,
    },
    finishButton: {
        backgroundColor: '#C09B72',
        paddingVertical: 18,
        paddingHorizontal: 20,
        borderRadius: 12,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 3},
        shadowOpacity: 0.15,
        shadowRadius: 5,
        elevation: 3,
    },
    finishButtonText: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: 16,
    },
    scrollContainer: {
        paddingVertical: 0,
    },
    createGroupInfoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        backgroundColor: '#F8F8F8',
        borderRadius: 12,
        marginTop: 16,
        marginBottom: 100,
        borderWidth: 1,
        borderColor: '#E8E8E8',
    },
    createGroupInfoIcon: {
        fontSize: 24,
        marginRight: 8,
    },
    createGroupInfoText: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '500',
    },
    createGroupButton: {
        backgroundColor: '#C09B72',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 10,
        marginTop: 16,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
    },
    createGroupButtonText: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: 16,
        textAlign: 'center',
    },
    loadingButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingButtonText: {
        marginLeft: 8,
    },
    aiTagContainer: {
        backgroundColor: '#E1F5FE',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#81D4FA',
    },
    aiTag: {
        fontSize: 12,
        color: '#0288D1',
        fontWeight: '500',
    },
    continueLink: {
        color: '#C09B72',
        fontSize: 16,
        fontWeight: '600',
        marginTop: 16,
        textDecorationLine: 'underline',
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContent: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 24,
        width: '100%',
        maxWidth: 400,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
    modalTitle: {
        fontSize: 20,
        fontWeight: '700',
        color: '#333333',
        textAlign: 'center',
        marginBottom: 8,
    },
    modalSubtitle: {
        fontSize: 16,
        color: '#666666',
        textAlign: 'center',
        marginBottom: 20,
    },
    modalInput: {
        borderWidth: 1,
        borderColor: '#D8D8D8',
        borderRadius: 12,
        padding: 16,
        fontSize: 16,
        backgroundColor: '#F8F8F8',
        marginBottom: 20,
    },
    modalButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 12,
    },
    modalButton: {
        flex: 1,
        paddingVertical: 14,
        borderRadius: 10,
        alignItems: 'center',
    },
    modalCancelButton: {
        backgroundColor: '#F5F5F5',
        borderWidth: 1,
        borderColor: '#D8D8D8',
    },
    modalCancelButtonText: {
        color: '#666666',
        fontWeight: '600',
        fontSize: 16,
    },
    modalCreateButton: {
        backgroundColor: '#C09B72',
    },
    modalCreateButtonText: {
        color: '#FFFFFF',
        fontWeight: '600',
        fontSize: 16,
    },
    modalButtonDisabled: {
        backgroundColor: '#E9E1D8',
        opacity: 0.6,
    },
});

export default Onboarding;
