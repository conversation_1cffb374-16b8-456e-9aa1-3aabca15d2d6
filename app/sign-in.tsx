import React, {useState} from 'react';
import {router} from 'expo-router';
import {
    StyleSheet,
    Text,
    TextInput,
    View,
    Image,
    TouchableOpacity,
    ActivityIndicator,
    Platform,
    Dimensions
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {
    GoogleAuthProvider,
    signInWithCredential,
    signInWithEmailAndPassword,
} from 'firebase/auth';
import {GoogleSignin} from '@react-native-google-signin/google-signin';
import {auth, functions} from '@/config/firebaseConfig';
import {useSession} from '../ctx';
import TitleTagline from '@/components/TitleTagline';
import {httpsCallable} from 'firebase/functions';

GoogleSignin.configure({
    webClientId: '982232823164-46u9kqsh1vcduu1lc1hs0tli6879vr04.apps.googleusercontent.com',
    iosClientId: '982232823164-sg1v7ronorbmnv9n5ghfc715fsimhrne.apps.googleusercontent.com',
    offlineAccess: true,
});

const GoogleLogo = () => (
    <Image
        source={require('../assets/images/google-logo.webp')}
        style={styles.googleLogo}
        resizeMode="contain"
    />
);

export default function SignIn() {
    const {signIn} = useSession();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [isSigningIn, setIsSigningIn] = useState(false);
    const [isGoogleSigningIn, setIsGoogleSigningIn] = useState(false);

    const showError = (message: string) => {
        setErrorMessage(message);
        setTimeout(() => setErrorMessage(''), 5000);
    };

    const handleSignIn = async () => {
        if (!email || !password) {
            showError('Please enter both email and password');
            return;
        }

        setIsSigningIn(true);
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            signIn(user, user.refreshToken);
            
            // Check user profile and route accordingly
            await checkUserProfileAndRoute(user.uid);
        } catch (error: any) {
            let message = 'Incorrect credentials. Please try again.';

            switch (error.code) {
                case 'auth/invalid-email':
                    message = 'Please enter a valid email address.';
                    break;
                case 'auth/user-not-found':
                    message = 'No account found with this email. Please sign up.';
                    break;
                case 'auth/wrong-password':
                    message = 'Incorrect password. Please try again.';
                    break;
            }

            showError(message);
        } finally {
            setIsSigningIn(false);
        }
    };

    const handleGoogleSignIn = async () => {
        setIsGoogleSigningIn(true);
        try {
            await GoogleSignin.hasPlayServices({showPlayServicesUpdateDialog: true});
            await GoogleSignin.signOut(); // Clear previous state

            const signInResult = await GoogleSignin.signIn();
            const idToken = extractIdToken(signInResult);

            if (!idToken) {
                throw new Error('No ID token found in Google Sign-In response');
            }

            const googleCredential = GoogleAuthProvider.credential(idToken);
            const userCredential = await signInWithCredential(auth, googleCredential);
            const user = userCredential.user;

            signIn(user, user.refreshToken || '');
            
            // Check user profile and route accordingly
            await checkUserProfileAndRoute(user.uid);
        } catch (error: any) {
            console.error('Google Sign-In error:', error);
            showError(`Google Sign-In failed: ${error.message || 'Unknown error'}`);
        } finally {
            setIsGoogleSigningIn(false);
        }
    };

    const extractIdToken = (result: any): string | null => {
        // Try direct access first
        if (result?.idToken) return result.idToken;
        if (result?.user?.idToken) return result.user.idToken;
        
        // Recursive search for token-like properties
        const findToken = (obj: any): string | null => {
            if (!obj || typeof obj !== 'object') return null;
            
            const tokenProps = ['idToken', 'id_token', 'token', 'accessToken'];
            
            for (const prop of tokenProps) {
                if (obj[prop] && typeof obj[prop] === 'string' && obj[prop].length > 20) {
                    return obj[prop];
                }
            }
            
            for (const key in obj) {
                if (typeof obj[key] === 'object' && obj[key] !== null) {
                    const found = findToken(obj[key]);
                    if (found) return found;
                }
            }
            
            return null;
        };
        
        return findToken(result);
    };

    const checkUserProfileAndRoute = async (userId: string) => {
        try {
            const checkUserProfileCall = httpsCallable(functions, 'checkUserProfile');
            const result = await checkUserProfileCall({userId});
            const profileData = result.data as any;

            if (profileData.needsOnboarding) {
                router.push({
                    pathname: '/onboarding',
                    params: {
                        userNameParam: auth.currentUser?.displayName || '',
                    },
                });
            } else {
                router.replace('/');
            }
        } catch (error) {
            console.error('Error checking user profile:', error);
            // Fallback to onboarding on error
            router.push({
                pathname: '/onboarding',
                params: {
                    userNameParam: auth.currentUser?.displayName || '',
                },
            });
        }
    };

    return (
        <KeyboardAwareScrollView
            enableOnAndroid={true}
            enableAutomaticScroll={Platform.OS === 'ios'}
            keyboardShouldPersistTaps="handled"
            extraScrollHeight={20}
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            style={styles.container}>

            <TitleTagline />

            <View style={styles.formContainer}>
                <Text style={styles.formTitle}>Welcome Back :)</Text>

                <TextInput
                    style={styles.input}
                    placeholder="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    placeholderTextColor="#999"
                />

                <TextInput
                    style={styles.input}
                    placeholder="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                    placeholderTextColor="#999"
                />

                <View style={styles.forgotPasswordContainer}>
                    <TouchableOpacity onPress={() => router.push('/resetPassword')}>
                        <Text style={styles.forgotPasswordText}>
                            Forgot password?
                        </Text>
                    </TouchableOpacity>
                </View>

                <TouchableOpacity
                    style={styles.signInButton}
                    onPress={handleSignIn}
                    disabled={isSigningIn}>
                    {isSigningIn ? (
                        <ActivityIndicator color="#fff" size="small" />
                    ) : (
                        <Text style={styles.signInButtonText}>Sign In</Text>
                    )}
                </TouchableOpacity>

                <View style={{height: 20}} />

                <View style={styles.orDivider}>
                    <View style={styles.dividerLine} />
                    <Text style={styles.orText}>OR</Text>
                    <View style={styles.dividerLine} />
                </View>

                <TouchableOpacity
                    style={styles.googleButton}
                    onPress={handleGoogleSignIn}
                    disabled={isGoogleSigningIn}>
                    {isGoogleSigningIn ? (
                        <ActivityIndicator color="#4285F4" size="small" />
                    ) : (
                        <>
                            <GoogleLogo />
                            <Text style={styles.googleButtonText}>Continue with Google</Text>
                        </>
                    )}
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.linkContainer}
                    onPress={() => router.push('/sign-up')}>
                    <Text style={styles.link}>
                        Don't have an account? <Text style={styles.linkBold}>Sign Up</Text>
                    </Text>
                </TouchableOpacity>
            </View>

            {errorMessage ? (
                <View style={styles.flashMessage}>
                    <Text style={styles.flashText}>{errorMessage}</Text>
                </View>
            ) : null}
        </KeyboardAwareScrollView>
    );
}

const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
    },
    formContainer: {
        width: '100%',
        maxWidth: 400,
        alignItems: 'center',
        marginTop: 20,
    },
    formTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 24,
        color: '#333',
        textAlign: 'center',
    },
    input: {
        width: '100%',
        height: 50,
        borderColor: '#ddd',
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 16,
        paddingHorizontal: 16,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
        color: '#333',
    },
    signInButton: {
        width: '100%',
        height: 50,
        backgroundColor: '#C09B72',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    signInButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    orDivider: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 24,
    },
    dividerLine: {
        flex: 1,
        height: 1,
        backgroundColor: '#e0e0e0',
    },
    orText: {
        marginHorizontal: 16,
        color: '#999',
        fontSize: 14,
    },
    googleButton: {
        width: '100%',
        height: 50,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: 16,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 1},
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    googleLogo: {
        width: 24,
        height: 24,
        marginRight: 12,
    },
    googleButtonText: {
        color: '#757575',
        fontSize: 16,
        fontWeight: '500',
    },
    linkContainer: {
        marginTop: 24,
        paddingVertical: 8,
    },
    link: {
        color: '#666',
        fontSize: 16,
    },
    linkBold: {
        color: '#C09B72',
        fontWeight: 'bold',
    },
    flashMessage: {
        position: 'absolute',
        bottom: 20,
        width: '90%',
        backgroundColor: '#333',
        padding: 16,
        borderRadius: 8,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5,
    },
    flashText: {
        color: 'white',
        fontSize: 14,
        textAlign: 'center',
    },
    forgotPasswordContainer: {
        width: '100%',
        alignItems: 'center',
        marginBottom: 16,
    },
    forgotPasswordText: {
        color: '#C09B72',
        fontSize: 14,
    },
});