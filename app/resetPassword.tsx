import React, {useState} from 'react';
import {router} from 'expo-router';
import {
    ActivityIndicator,
    Platform,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {sendPasswordResetEmail} from 'firebase/auth';
import {auth} from '@/config/firebaseConfig';
import TitleTagline from '@/components/TitleTagline';
import Toast from 'react-native-toast-message';

export default function ResetPassword() {
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isEmailSent, setIsEmailSent] = useState(false);

    const handleSendResetEmail = async () => {
        if (!email) {
            Toast.show({
                type: 'error',
                text1: 'Please enter your email address',
                position: 'top',
            });
            return;
        }

        setIsLoading(true);
        await sendPasswordResetEmail(auth, email);
        setIsEmailSent(true);
        Toast.show({
            type: 'success',
            text1: 'Reset password email sent',
            text2: 'Check your inbox for instructions',
            position: 'top',
        });
        setIsLoading(false);
    };

    return (
        <KeyboardAwareScrollView
            enableOnAndroid={true}
            enableAutomaticScroll={Platform.OS === 'ios'}
            keyboardShouldPersistTaps="handled"
            extraScrollHeight={20}
            contentContainerStyle={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            style={styles.container}>
            
            <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}>
                <Text style={styles.backButtonText}>← Back</Text>
            </TouchableOpacity>

            <TitleTagline />

            <View style={styles.formContainer}>
                <Text style={styles.formTitle}>Reset Your Password</Text>
                <Text style={styles.formDescription}>
                    Enter your email address and we'll send you a link to
                    reset your password.
                </Text>

                <TextInput
                    style={styles.input}
                    placeholder="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    placeholderTextColor="#999"
                    editable={!isEmailSent}
                />

                {isEmailSent ? (
                    <View style={styles.confirmationContainer}>
                        <Text style={styles.confirmationText}>
                            Please check your email and follow the link to
                            reset your password.
                        </Text>
                        <TouchableOpacity
                            style={styles.secondaryButton}
                            onPress={() => router.push('/sign-in')}>
                            <Text style={styles.secondaryButtonText}>
                                Return to Sign In
                            </Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    <TouchableOpacity
                        style={styles.resetButton}
                        onPress={handleSendResetEmail}
                        disabled={isLoading}>
                        {isLoading ? (
                            <ActivityIndicator color="#fff" size="small" />
                        ) : (
                            <Text style={styles.resetButtonText}>
                                Send Reset Email
                            </Text>
                        )}
                    </TouchableOpacity>
                )}
            </View>
        </KeyboardAwareScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollContainer: {
        flexGrow: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
    },
    backButton: {
        position: 'absolute',
        top: 50,
        left: 20,
        padding: 10,
    },
    backButtonText: {
        fontSize: 16,
        color: '#666',
    },
    formContainer: {
        width: '100%',
        maxWidth: 400,
        alignItems: 'center',
        marginTop: 20,
    },
    formTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 16,
        color: '#333',
        textAlign: 'center',
    },
    formDescription: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        marginBottom: 24,
    },
    input: {
        width: '100%',
        height: 50,
        borderColor: '#ddd',
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 16,
        paddingHorizontal: 16,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
        color: '#333',
    },
    resetButton: {
        width: '100%',
        height: 50,
        backgroundColor: '#C09B72',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: {width: 0, height: 2},
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    resetButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    confirmationContainer: {
        width: '100%',
        alignItems: 'center',
        marginTop: 16,
    },
    confirmationText: {
        fontSize: 16,
        color: '#333',
        textAlign: 'center',
        marginBottom: 24,
    },
    secondaryButton: {
        width: '100%',
        height: 50,
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: '#C09B72',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    secondaryButtonText: {
        color: '#C09B72',
        fontSize: 16,
        fontWeight: '500',
    },
});
