# Push Notification Setup Guide

This guide will help you properly set up push notifications in your Expo React Native app using Firebase Cloud Messaging (FCM).

## Current Implementation

We've implemented a dual approach to push notifications:
1. Firebase Cloud Messaging (FCM) as the primary method
2. Expo Notifications as a fallback if FCM isn't available

## Firebase Cloud Messaging Setup

### Step 1: Configure Firebase Console

1. Go to the Firebase Console: https://console.firebase.google.com/
2. Select your project (sootro-18482)
3. Go to Project Settings
4. In the "Cloud Messaging" tab, scroll to the "Web configuration" section
5. Click on "Generate Key Pair" to get your VAPID key
6. Take note of the public key that's generated

### Step 2: Set Environment Variables

Create or update your environment variables to include the VAPID key:

```
EXPO_PUBLIC_VAPID_KEY=<your_vapid_key_from_firebase_console>
```

### Step 3: Service Worker Setup

1. Make sure the `firebase-messaging-sw.js` file is in the root of your web server
2. Verify that the Firebase configuration in the service worker matches your app's configuration

### Step 4: Verify FCM Registration

Our implementation in `utils/pushNotifications.ts` now tries to get an FCM token first, and falls back to Expo notifications if FCM isn't available. The implementation:

1. Requests notification permission
2. Gets an FCM token using the VAPID key
3. Saves the token to Firestore via Cloud Functions

## Managing Push Notifications 

### Sending Notifications

Firebase Cloud Functions handle sending push notifications when:
- A new message is sent in a chat
- Other notification-worthy events occur

The function that sends a notification:
```javascript
// In functions/src/index.ts in the sendMessage function
const message = {
  notification: {
    title: `New message from ${senderName}`,
    body: text.length > 100 ? text.substring(0, 97) + '...' : text,
  },
  data: {
    chatId: chatId,
    senderId: currentUserId,
    type: 'new_message',
  },
  token: fcmToken,
};

await admin.messaging().send(message);
```

### Receiving Notifications

Notifications are received and handled in two ways:

1. **Foreground**: Managed by the `configurePushNotifications` function which calls `Notifications.setNotificationHandler`
2. **Background**: Handled by the Firebase service worker

## Troubleshooting

If you're experiencing issues with push notifications:

1. **Web Notification APIs**: Ensure you're using a secure context (HTTPS) for web notifications
2. **Firebase Console**: Check for any errors in the Firebase console logs
3. **VAPID Key**: Make sure your VAPID key is correctly set in your environment
4. **Device Testing**: Test on physical devices, as emulators/simulators may not support push notifications
5. **Web Service Worker**: Verify the service worker is registered correctly for web

For more detailed information, refer to:
- [Firebase Cloud Messaging for JavaScript](https://firebase.google.com/docs/cloud-messaging/js/client)
- [Expo Push Notifications Documentation](https://docs.expo.dev/push-notifications/overview/) 