/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

/**
 * App color scheme based on onboarding design
 */

export const Colors = {
  // Primary colors
  primary: '#C09B72',        // Main brown/gold color used for buttons and accents
  primaryLight: '#F9F4EE',   // Light cream background
  primaryDark: '#8A6E52',    // Darker brown for text
  
  // Background colors
  background: '#FFFFFF',     // Main background color
  backgroundSecondary: '#F8F8F8', // Secondary background for inputs, etc.
  backgroundGray: '#F5F5F5', // Gray background for cards and containers
  
  // Text colors
  text: '#333333',           // Primary text color
  textSecondary: '#666666',  // Secondary/subtitle text color
  textTertiary: '#888888',   // Less important text
  textLight: '#FFFFFF',      // Text on dark backgrounds
  
  // Border colors
  border: '#E8E8E8',         // Light border color
  borderAccent: '#E9E1D8',   // Accent border color
  borderLight: '#EEEEEE',    // Light gray border color
  
  // Status colors
  error: '#FF6B6B',
  success: '#4CAF50',
  info: '#2196F3',
  warning: '#FFC107',
  
  // Additional colors for specific components
  shadow: '#000000',

  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#ECEDEE',
    background: '#151718',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
};

export default Colors;
