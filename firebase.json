{"firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"], "host": "0.0.0.0"}], "emulators": {"storage": {"port": 9199}, "functions": {"port": 5001, "host": "0.0.0.0"}, "ui": {"enabled": true}, "singleProjectMode": true}}