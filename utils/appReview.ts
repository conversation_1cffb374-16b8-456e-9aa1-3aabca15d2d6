import InAppReview from 'react-native-in-app-review';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Keys for AsyncStorage
const LAST_REVIEW_REQUEST_TIME = 'lastReviewRequestTime';
const REVIEW_REQUESTED_COUNT = 'reviewRequestedCount';
const GROUP_LEAVE_COUNT = 'groupLeaveCount';

// Configuration
const MIN_DAYS_BETWEEN_REQUESTS = 14; // Minimum days between review requests
const MAX_LIFETIME_REQUESTS = 3; // Maximum number of review requests during app lifetime
const GROUP_LEAVE_THRESHOLD = 2; // Show review after leaving this many groups

/**
 * Check if we should show a review request
 */
export const shouldShowReview = async (): Promise<boolean> => {
  try {
    // Check if in-app review is available on this device
    if (!InAppReview.isAvailable()) {
      return false;
    }

    // Get last request time
    const lastRequestTimeStr = await AsyncStorage.getItem(LAST_REVIEW_REQUEST_TIME);
    const lastRequestTime = lastRequestTimeStr ? parseInt(lastRequestTimeStr) : 0;
    
    // Get number of times review requested
    const reviewCountStr = await AsyncStorage.getItem(REVIEW_REQUESTED_COUNT);
    const reviewCount = reviewCountStr ? parseInt(reviewCountStr) : 0;
    
    // Check if we've reached the lifetime maximum
    if (reviewCount >= MAX_LIFETIME_REQUESTS) {
      return false;
    }
    
    // Check if enough time has passed since last request
    const now = Date.now();
    const daysSinceLastRequest = (now - lastRequestTime) / (1000 * 60 * 60 * 24);
    
    return daysSinceLastRequest >= MIN_DAYS_BETWEEN_REQUESTS;
  } catch (error) {
    console.error('Error checking review conditions:', error);
    return false;
  }
};

/**
 * Increment counter for group leaves and check if we should show a review
 */
export const handleGroupLeave = async (): Promise<boolean> => {
  try {
    // Increment group leave counter
    const leaveCountStr = await AsyncStorage.getItem(GROUP_LEAVE_COUNT);
    const leaveCount = leaveCountStr ? parseInt(leaveCountStr) : 0;
    const newLeaveCount = leaveCount + 1;
    await AsyncStorage.setItem(GROUP_LEAVE_COUNT, newLeaveCount.toString());
    
    // Check if we've reached threshold of group leaves and should show review
    if (newLeaveCount >= GROUP_LEAVE_THRESHOLD) {
      return await shouldShowReview();
    }
    
    return false;
  } catch (error) {
    console.error('Error handling group leave:', error);
    return false;
  }
};

/**
 * Request an in-app review
 */
export const requestReview = async (): Promise<void> => {
  try {
    // Update review request metrics
    const reviewCountStr = await AsyncStorage.getItem(REVIEW_REQUESTED_COUNT);
    const reviewCount = reviewCountStr ? parseInt(reviewCountStr) : 0;
    await AsyncStorage.setItem(REVIEW_REQUESTED_COUNT, (reviewCount + 1).toString());
    await AsyncStorage.setItem(LAST_REVIEW_REQUEST_TIME, Date.now().toString());
    
    // Reset group leave counter after requesting review
    await AsyncStorage.setItem(GROUP_LEAVE_COUNT, '0');
    
    // Request the review
    await InAppReview.RequestInAppReview();
  } catch (error) {
    console.error('Error requesting app review:', error);
  }
};

/**
 * Utility to clear all review-related data 
 * (useful for testing)
 */
export const resetReviewData = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(LAST_REVIEW_REQUEST_TIME);
    await AsyncStorage.removeItem(REVIEW_REQUESTED_COUNT);
    await AsyncStorage.removeItem(GROUP_LEAVE_COUNT);
  } catch (error) {
    console.error('Error resetting review data:', error);
  }
}; 