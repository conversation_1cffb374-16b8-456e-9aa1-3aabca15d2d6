import {
    deleteObject,
    getDownloadURL,
    ref,
    uploadBytesResumable,
} from 'firebase/storage';
import {doc, Firestore, setDoc} from 'firebase/firestore';
import {uuid} from 'expo-modules-core';
import Toast from 'react-native-toast-message';
import {storage} from '@/config/firebaseConfig';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { Image } from 'react-native';

// Maximum image size in bytes (1MB)
const MAX_IMAGE_SIZE = 1024 * 1024;
// Maximum height/width for images (reducing large images)
const MAX_IMAGE_DIMENSION = 1200;

/**
 * Optimizes an image by reducing quality and/or dimensions
 * @param imageUri URI of the image to optimize
 * @returns URI of the optimized image
 */
const optimizeImage = async (imageUri: string): Promise<string> => {
    try {
        // First get the file info to see if optimization is needed
        const fileInfo = await FileSystem.getInfoAsync(imageUri, { size: true });
        
        if (!fileInfo.exists) {
            console.log('Image file does not exist');
            return imageUri;
        }
        
        // Check if image is already small enough
        if ('size' in fileInfo && fileInfo.size <= MAX_IMAGE_SIZE) {
            console.log('Image already optimized, skipping compression');
            return imageUri;
        }

        // Use ImageManipulator to get dimensions
        const imageInfo = await ImageManipulator.manipulateAsync(
            imageUri,
            [],
            { compress: 1 }
        );
        
        // Calculate scaling factor if needed
        let scaleFactor = 1;
        const { width, height } = imageInfo;
        if (width && height) {
            const maxDimension = Math.max(width, height);
            if (maxDimension > MAX_IMAGE_DIMENSION) {
                scaleFactor = MAX_IMAGE_DIMENSION / maxDimension;
            }
        }
        
        // Initial quality (start with moderate compression)
        let quality = 0.7;
        
        // If the image is very large, use more aggressive compression
        if ('size' in fileInfo && fileInfo.size > MAX_IMAGE_SIZE * 3) {
            quality = 0.5;
        }
        
        // Calculate new dimensions
        const newWidth = Math.round(width * scaleFactor);
        const newHeight = Math.round(height * scaleFactor);
        
        // Use ImageManipulator to resize and compress the image
        const manipResult = await ImageManipulator.manipulateAsync(
            imageUri,
            [{ resize: { width: newWidth, height: newHeight } }],
            { compress: quality, format: ImageManipulator.SaveFormat.JPEG }
        );
        
        // Log the optimization results
        const originalSize = 'size' in fileInfo ? fileInfo.size : 'unknown';
        const newFileInfo = await FileSystem.getInfoAsync(manipResult.uri, { size: true });
        const newSize = 'size' in newFileInfo ? newFileInfo.size : 'unknown';
        console.log(`Image optimized: ${originalSize} bytes -> ${newSize} bytes`);
        
        return manipResult.uri;
    } catch (error) {
        console.error('Error optimizing image:', error);
        // Return original URI if optimization fails
        return imageUri;
    }
};

interface SaveProfileImagesParams {
    db: Firestore;
    userId: string;
    savedImages: string[];
    newImages: ImagePicker.ImagePickerAsset[];
    imagesToDelete: string[];
    setIsSubmitting?: (isSubmitting: boolean) => void;
    onSuccess?: (finalImages: string[]) => void;
    onError?: (error: Error) => void;
    additionalData?: Record<string, any>;
}

/**
 * Handles saving profile images by uploading new images, deleting marked images,
 * and updating the Firestore document.
 */
export const saveProfileImages = async ({
    db,
    userId,
    savedImages,
    newImages,
    imagesToDelete,
    setIsSubmitting,
    onSuccess,
    onError,
    additionalData = {},
}: SaveProfileImagesParams): Promise<string[] | null> => {
    // Validate inputs
    if (!userId) {
        console.error('No user ID provided when saving profile images');
        Toast.show({
            type: 'error',
            text1: 'Authentication error',
            text2: 'Please try signing in again',
        });
        return null;
    }

    // Check that we have at least one image
    const totalImages =
        savedImages.length + newImages.length - imagesToDelete.length;
    if (totalImages === 0) {
        Toast.show({
            type: 'error',
            text1: 'No images',
            text2: 'At least one image is required.',
        });
        return null;
    }

    // Set loading state if provided
    if (setIsSubmitting) {
        setIsSubmitting(true);
    }

    try {
        console.log('Starting image upload process');
        let uploadedUrls: string[] = [];

        // Upload new images
        if (newImages.length > 0) {
            console.log(`Uploading ${newImages.length} new images`);
            // Process images one by one instead of all at once to avoid memory issues
            for (const image of newImages) {
                try {
                    const imageId = uuid.v4();
                    console.log(
                        `Processing image with generated ID: ${imageId}`,
                    );
                    // Optimize the image before uploading
                    const optimizedImageUri = await optimizeImage(image.uri);
                    const response = await fetch(optimizedImageUri);
                    const blob = await response.blob();
                    const storageRef = ref(
                        storage,
                        `profiles/${userId}/${imageId}.jpg`,
                    );
                    const uploadTask = await uploadBytesResumable(
                        storageRef,
                        blob,
                    );
                    const downloadUrl = await getDownloadURL(uploadTask.ref);
                    uploadedUrls.push(downloadUrl);
                    console.log(`Successfully uploaded image: ${imageId}`);
                } catch (innerError) {
                    console.error(
                        'Error uploading individual image:',
                        innerError,
                    );
                    // Continue with other images rather than failing completely
                }
            }
        }

        // Delete marked images
        if (imagesToDelete.length > 0) {
            console.log(`Deleting ${imagesToDelete.length} images`);
            for (const imageUrl of imagesToDelete) {
                try {
                    const storageRef = ref(storage, imageUrl);
                    await deleteObject(storageRef);
                    console.log(`Successfully deleted image: ${imageUrl}`);
                } catch (deleteError) {
                    console.error(
                        'Error deleting individual image:',
                        deleteError,
                    );
                    // Continue with other deletions rather than failing completely
                }
            }
        }

        // Update remaining images
        const remainingImages = savedImages.filter(
            img => !imagesToDelete.includes(img),
        );
        const finalImages = [...remainingImages, ...uploadedUrls];
        console.log(`Final image count: ${finalImages.length}`);

        // Check if we have at least one image after all operations
        if (finalImages.length === 0) {
            Toast.show({
                type: 'error',
                text1: 'Image upload failed',
                text2: 'Please try again with at least one image',
            });
            if (setIsSubmitting) {
                setIsSubmitting(false);
            }
            if (onError) {
                onError(new Error('No images after uploading'));
            }
            return null;
        }

        // Write updated images array to Firestore
        console.log('Saving image URLs to Firestore');
        const userDoc = doc(db, 'users', userId);
        await setDoc(
            userDoc,
            {
                images: finalImages,
                ...additionalData,
            },
            {merge: true},
        );

        // Show success message
        Toast.show({
            type: 'success',
            text1: 'Profile updated',
            text2: 'Your images have been saved.',
        });

        // Call success callback
        if (onSuccess) {
            onSuccess(finalImages);
        }

        return finalImages;
    } catch (error) {
        console.error('Error in saveProfileImages:', error);
        if (setIsSubmitting) {
            setIsSubmitting(false);
        }
        Toast.show({
            type: 'error',
            text1: 'Error saving images',
            text2:
                error instanceof Error
                    ? error.message
                    : 'Failed to save images',
        });
        if (onError) {
            onError(
                error instanceof Error ? error : new Error('Unknown error'),
            );
        }
        return null;
    }
};
