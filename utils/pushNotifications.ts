import { Platform } from 'react-native';
import { getAuth } from 'firebase/auth';
import { getFunctions, httpsCallable } from 'firebase/functions';
import messaging from '@react-native-firebase/messaging';
import { app } from '@/config/firebaseConfig';
import { VAPID_KEY, PROJECT_ID } from '../env';
import { 
  getPermissionsAsync, 
  requestPermissionsAsync, 
  getExpoPushTokenAsync,
  setNotificationChannelAsync,
  scheduleNotificationAsync,
  addNotificationResponseReceivedListener,
  addNotificationReceivedListener,
  removeNotificationSubscription,
  AndroidImportance,
  setNotificationHandler
} from 'expo-notifications';

// Handle missing Device module
let Device: any;
try {
  Device = require('expo-device');
} catch (error) {
  console.warn('Expo Device module not available:', error);
  Device = { isDevice: true };
}

// Function to check if we're on a physical device
export function isPhysicalDevice(): boolean {
  return Device.isDevice === undefined ? true : Device.isDevice;
}

/**
 * Sets up React Native Firebase message handlers
 */
function setupMessageHandlers(): () => void {
  // Handle background messages
  messaging().setBackgroundMessageHandler(async remoteMessage => {
    console.log('Message handled in the background!', remoteMessage);
  });

  // Handle foreground messages
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    console.log('A new FCM message arrived!', remoteMessage);
    
    // You can show a local notification here if needed
    if (remoteMessage.notification) {
      // For foreground messages, you might want to show a local notification
      // or update your app's UI directly
    }
  });

  // Handle token refresh
  messaging().onTokenRefresh(token => {
    console.log('FCM token refreshed:', token);
    // Save the new token
    saveFcmToken(token);
  });

  // Return unsubscribe function
  return unsubscribe;
}

/**
 * Requests permission and initializes Firebase Cloud Messaging
 */
export async function initializeFirebaseMessaging(): Promise<string | null> {
  // Don't attempt on non-physical devices
  if (!isPhysicalDevice()) {
    console.log('Skipping Firebase messaging initialization on non-physical device');
    return null;
  }
  
  try {
    console.log('Requesting notification permission...');
    
    // Request notification permission using React Native Firebase
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (!enabled) {
      console.log('Permission not granted for notifications');
      return null;
    }
    
    console.log('Notification permission granted.');
    
    // Get FCM token using React Native Firebase
    const fcmToken = await messaging().getToken();
    
    if (fcmToken) {
      console.log('FCM token received:', fcmToken);
      
      // Set up message handlers
      setupMessageHandlers();
      
      return fcmToken;
    } else {
      console.log('No FCM token received');
      return null;
    }
  } catch (error) {
    console.error('Error initializing Firebase messaging:', error);
    return null;
  }
}

/**
 * Registers for push notifications and returns the device push token
 * This will try to use Firebase Cloud Messaging first, then fall back to Expo
 */
export async function registerForPushNotificationsAsync(): Promise<string | null> {
  // Don't attempt on non-physical devices
  if (!isPhysicalDevice()) {
    console.log('Skipping push notification registration on non-physical device');
    return null;
  }
  
  try {
    // Try Firebase Cloud Messaging first
    let token = await initializeFirebaseMessaging().catch(error => {
      console.log('Firebase messaging initialization failed:', error);
      return null;
    });
    
    if (token) {
      return token;
    }
    
    // Fall back to Expo push notifications if Firebase fails
    console.log('Falling back to Expo notifications...');

    try {
      // Check if we have permission, otherwise request it
      const { status: existingStatus } = await getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await requestPermissionsAsync();
        finalStatus = status;
      }
      
      // If permission not granted, exit the function
      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return null;
      }
      
      // Get push token
      try {
        const expoPushToken = await getExpoPushTokenAsync({
          projectId: PROJECT_ID,
        });
        token = expoPushToken.data;
      } catch (error) {
        console.log('Error getting Expo push token:', error);
        return null;
      }
    } catch (error) {
      console.log('Error requesting notification permissions:', error);
      return null;
    }

    // Set notification handling for Android
    if (Platform.OS === 'android') {
      try {
        await setNotificationChannelAsync('default', {
          name: 'default',
          importance: AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      } catch (error) {
        console.log('Error setting Android notification channel:', error);
      }
    }
    
    return token;
  } catch (error) {
    console.error('Error setting up push notifications:', error);
    return null;
  }
}

/**
 * Saves FCM token to Firestore via Firebase function
 */
export async function saveFcmToken(token: string): Promise<boolean> {
  try {
    const auth = getAuth(app);
    const user = auth.currentUser;
    
    if (!user) {
      console.log('User not authenticated. Cannot save FCM token.');
      return false;
    }
    
    const functions = getFunctions(app);
    const updateFcmToken = httpsCallable(functions, 'updateFcmToken');
    
    const result = await updateFcmToken({ 
      userId: user.uid,
      fcmToken: token 
    });
    console.log('FCM token saved successfully:', result.data);
    return true;
  } catch (error) {
    console.error('Error saving FCM token:', error);
    return false;
  }
}

/**
 * Sets up push notifications by registering and saving the token
 */
export async function setupPushNotifications(): Promise<boolean> {
  // Skip push notification setup on non-physical devices
  if (!isPhysicalDevice()) {
    console.log('Skipping push notification setup on non-physical device');
    return false;
  }
  
  try {
    const token = await registerForPushNotificationsAsync();
    
    if (!token) {
      console.log('Failed to register for push notifications');
      return false;
    }
    
    console.log('Push notification token:', token);
    const saved = await saveFcmToken(token);
    
    if (!saved) {
      console.log('Failed to save FCM token');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error setting up push notifications:', error);
    return false;
  }
}

/**
 * Configures how notifications are handled when the app is open
 * @returns Function to clean up notification listeners
 */
export function configurePushNotifications(): () => void {
  // Skip notification configuration on non-physical devices
  if (!isPhysicalDevice()) {
    console.log('Skipping push notification configuration on non-physical device');
    return () => {};
  }
  
  try {
    // Set notification handler for when a notification is received while app is foregrounded
    setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
    
    // Listen for notification responses (when user taps on a notification)
    const responseListener = addNotificationResponseReceivedListener((response: any) => {
      console.log('Notification response received:', response);
      
      // Here you can handle notification taps
      // For example, navigate to specific screens based on notification data
      const data = response.notification.request.content.data;
      console.log('Notification data:', data);
      
      // Example: if (data.type === 'message') { navigate to messages screen }
    });
    
    // Listen for notifications received while app is foregrounded
    const receivedListener = addNotificationReceivedListener((notification: any) => {
      console.log('Notification received in foreground:', notification);
    });
    
    // Return a cleanup function to remove listeners when no longer needed
    return () => {
      removeNotificationSubscription(responseListener);
      removeNotificationSubscription(receivedListener);
    };
  } catch (error) {
    console.error('Error configuring push notifications:', error);
    return () => {};
  }
}

/**
 * Tests sending a notification locally
 */
export async function sendTestNotification(title: string, body: string): Promise<void> {
  try {
    await scheduleNotificationAsync({
      content: {
        title: title || 'Test Notification',
        body: body || 'This is a test notification',
        data: { test: 'data' },
      },
      trigger: null, // null means show immediately
    });
    console.log('Test notification sent');
  } catch (error) {
    console.error('Error sending test notification:', error);
  }
} 