import { httpsCallable, HttpsCallableResult } from 'firebase/functions';
import { Functions } from 'firebase/functions';
import { User } from 'firebase/auth';
import { auth } from '@/config/firebaseConfig';

export interface FirebaseFunctionError {
  code: string;
  message: string;
  details?: any;
}

export async function callFirebaseFunction<T = any>(
  functions: Functions,
  functionName: string,
  data?: any,
  user?: User | null
): Promise<T> {
  // Get current user if not provided
  const currentUser = user || auth.currentUser;
  
  if (!currentUser) {
    throw new Error('User must be authenticated to call Firebase functions');
  }

  // Force token refresh to ensure we have a valid token
  try {
    await currentUser.getIdToken(true);
  } catch (error) {
    console.error('Failed to refresh authentication token:', error);
    throw new Error('Authentication token refresh failed');
  }

  try {
    const functionCall = httpsCallable(functions, functionName);
    const result: HttpsCallableResult<T> = await functionCall(data);
    return result.data;
  } catch (error: any) {
    console.error(`<PERSON>rror calling ${functionName}:`, error);
    
    // Handle specific Firebase function errors
    if (error.code === 'functions/unauthenticated') {
      throw new Error('Authentication required. Please sign in again.');
    } else if (error.code === 'functions/permission-denied') {
      throw new Error('Permission denied. You may not have access to this resource.');
    } else if (error.code === 'functions/invalid-argument') {
      throw new Error('Invalid arguments provided to the function.');
    } else if (error.code === 'functions/not-found') {
      throw new Error('The requested resource was not found.');
    } else if (error.code === 'functions/already-exists') {
      throw new Error('The resource already exists.');
    } else if (error.code === 'functions/resource-exhausted') {
      throw new Error('Resource exhausted. Please try again later.');
    } else if (error.code === 'functions/failed-precondition') {
      throw new Error('Operation failed due to a precondition not being met.');
    } else if (error.code === 'functions/aborted') {
      throw new Error('Operation was aborted.');
    } else if (error.code === 'functions/out-of-range') {
      throw new Error('Operation is out of range.');
    } else if (error.code === 'functions/unimplemented') {
      throw new Error('Operation is not implemented.');
    } else if (error.code === 'functions/internal') {
      throw new Error('Internal error occurred. Please try again.');
    } else if (error.code === 'functions/unavailable') {
      throw new Error('Service is currently unavailable. Please try again later.');
    } else if (error.code === 'functions/data-loss') {
      throw new Error('Data loss occurred.');
    } else if (error.code === 'functions/app-check-token-invalid') {
      throw new Error('App verification failed. Please restart the app and try again.');
    } else if (error.code === 'functions/app-check-token-expired') {
      throw new Error('App verification expired. Please restart the app and try again.');
    } else {
      // Generic error handling
      const errorMessage = error.message || 'An unknown error occurred';
      throw new Error(errorMessage);
    }
  }
}

// Helper function to check if user is authenticated
export function isUserAuthenticated(): boolean {
  return !!auth.currentUser;
}

// Helper function to get current user
export function getCurrentUser(): User | null {
  return auth.currentUser;
}

// Helper function to wait for authentication
export function waitForAuth(): Promise<User> {
  return new Promise((resolve, reject) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      unsubscribe();
      if (user) {
        resolve(user);
      } else {
        reject(new Error('No authenticated user found'));
      }
    });
  });
}
