import { Platform } from 'react-native';
import { getAppCheckInstance, getAppCheckToken, isAppCheckReady } from '@/config/firebaseConfig';

/**
 * Utility functions for App Check debugging and testing
 */

export interface AppCheckStatus {
  isInitialized: boolean;
  platform: string;
  isDevelopment: boolean;
  hasToken: boolean;
  tokenPreview?: string;
  error?: string;
}

/**
 * Get comprehensive App Check status for debugging
 */
export const getAppCheckStatus = async (): Promise<AppCheckStatus> => {
  const status: AppCheckStatus = {
    isInitialized: isAppCheckReady(),
    platform: Platform.OS,
    isDevelopment: __DEV__,
    hasToken: false,
  };

  try {
    if (status.isInitialized) {
      const token = await getAppCheckToken();
      if (token) {
        status.hasToken = true;
        // Show first 20 characters of token for debugging
        status.tokenPreview = `${token.substring(0, 20)}...`;
      }
    }
  } catch (error) {
    status.error = error instanceof Error ? error.message : 'Unknown error';
  }

  return status;
};

/**
 * Log App Check status to console with emojis for easy debugging
 */
export const logAppCheckStatus = async (): Promise<void> => {
  const status = await getAppCheckStatus();
  
  console.log('🔐 App Check Status Report:');
  console.log(`   📱 Platform: ${status.platform}`);
  console.log(`   🛠️  Development Mode: ${status.isDevelopment ? 'Yes' : 'No'}`);
  console.log(`   ✅ Initialized: ${status.isInitialized ? 'Yes' : 'No'}`);
  console.log(`   🎫 Has Token: ${status.hasToken ? 'Yes' : 'No'}`);
  
  if (status.tokenPreview) {
    console.log(`   🔑 Token Preview: ${status.tokenPreview}`);
  }
  
  if (status.error) {
    console.log(`   ❌ Error: ${status.error}`);
  }
  
  if (status.isDevelopment) {
    console.log('');
    console.log('🚨 DEVELOPMENT MODE NOTES:');
    console.log('   • Make sure you have added your debug tokens to Firebase Console');
    console.log('   • iOS Simulator: Add iOS debug token');
    console.log('   • Android Emulator: Add Android debug token');
    console.log('   • Debug tokens can be found in your firebaseConfig.js file');
  }
};

/**
 * Test App Check token retrieval
 */
export const testAppCheckToken = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing App Check token retrieval...');
    
    if (!isAppCheckReady()) {
      console.log('❌ App Check is not initialized');
      return false;
    }
    
    const token = await getAppCheckToken();
    
    if (token) {
      console.log('✅ App Check token retrieved successfully');
      console.log(`   Token length: ${token.length} characters`);
      return true;
    } else {
      console.log('❌ Failed to retrieve App Check token');
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing App Check token:', error);
    return false;
  }
};

/**
 * Get debug token instructions for the current platform
 */
export const getDebugTokenInstructions = (): string => {
  const platform = Platform.OS;
  
  if (platform === 'ios') {
    return `
🍎 iOS Debug Token Setup:
1. Open your iOS Simulator
2. Run your app in development mode
3. Check the console logs for the debug token
4. Copy the debug token from the logs
5. Go to Firebase Console > Project Settings > App Check
6. Add the debug token for your iOS app
7. Update the iOS debug token in config/firebaseConfig.js
`;
  } else if (platform === 'android') {
    return `
🤖 Android Debug Token Setup:
1. Open your Android Emulator
2. Run your app in development mode
3. Check the console logs for the debug token
4. Copy the debug token from the logs
5. Go to Firebase Console > Project Settings > App Check
6. Add the debug token for your Android app
7. Update the Android debug token in config/firebaseConfig.js
`;
  } else {
    return `
⚠️ Web platform detected. App Check debug tokens are not needed for web development.
For web, you would typically use reCAPTCHA v3 in production.
`;
  }
};

/**
 * Print debug token setup instructions
 */
export const printDebugTokenInstructions = (): void => {
  console.log(getDebugTokenInstructions());
};
