# Node modules
node_modules/

# Expo folders
.expo/
.expo-shared/

# Build directories
/dist
/build

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log
.history
settings.json

# Android build artifacts
android/app/build/
android/.gradle/
android/build/

# iOS build artifacts
ios/build/
ios/Pods/
ios/Sootro/SootroDebug.entitlements

# Environment files (adjust as needed)
.env
.env.*.local

# macOS system files
.DS_Store

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

#IDE files
.idea/

# Keystore files and credentials
android/app/*.keystore
android/app/*.jks
android/gradle.properties
android/keystore.properties
android/**/*.keystore
android/**/*.jks
*.keystore
*.jks
keystore.properties
pc-api-key.json
google-services.json
GoogleService-Info.plist
GoogleService-Info*.plist

