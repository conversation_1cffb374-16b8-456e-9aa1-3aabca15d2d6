# Firebase App Check Setup Guide

This guide explains how to set up Firebase App Check for your React Native app with proper debug token configuration for development.

## Overview

Firebase App Check helps protect your Firebase resources from abuse by verifying that requests come from your authentic app. In development mode, we use debug tokens to bypass the normal attestation process.

## Current Configuration

The app is configured to automatically:
- Use **debug providers** in development mode (`__DEV__ === true`)
- Use **production providers** in release builds
- Support both **iOS** and **Android** platforms
- Automatically detect the platform and use appropriate tokens

## Debug Token Setup

### Step 1: Get Your Debug Tokens

Debug tokens are already configured in `config/firebaseConfig.js`:

```javascript
const APP_CHECK_DEBUG_TOKENS = {
  ios: 'D65AAB80-B0CE-47E1-AFA1-D8C6D009D69D',     // Replace with your iOS debug token
  android: 'DB439970-F799-4EFA-B695-4E00B3FCA154', // Replace with your Android debug token
};
```

**⚠️ Important**: You need to replace these with your actual debug tokens from Firebase Console.

### Step 2: Add Debug Tokens to Firebase Console

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`sootro-18482`)
3. Go to **Project Settings** > **App Check**
4. For each app (iOS and Android):
   - Click **Manage debug tokens**
   - Click **Add debug token**
   - Enter the debug token from your `firebaseConfig.js`
   - Add a description (e.g., "iOS Development", "Android Development")
   - Save

### Step 3: Update Debug Tokens (if needed)

If you need to generate new debug tokens:

1. **For iOS Simulator**:
   - Run the app in development mode
   - Check the console logs for App Check debug token
   - Copy the token and update `APP_CHECK_DEBUG_TOKENS.ios`

2. **For Android Emulator**:
   - Run the app in development mode
   - Check the console logs for App Check debug token
   - Copy the token and update `APP_CHECK_DEBUG_TOKENS.android`

## Testing App Check

### Console Logging

The app automatically logs App Check status in development mode. Look for logs like:

```
🔐 App Check Status Report:
   📱 Platform: ios
   🛠️  Development Mode: Yes
   ✅ Initialized: Yes
   🎫 Has Token: Yes
   🔑 Token Preview: eyJhbGciOiJSUzI1NiI...
```

### Manual Testing

You can manually test App Check using the utility functions:

```javascript
import { logAppCheckStatus, testAppCheckToken } from '@/utils/appCheckUtils';

// Log current status
await logAppCheckStatus();

// Test token retrieval
const success = await testAppCheckToken();
```

## Platform-Specific Configuration

### iOS Configuration

- **Development**: Uses `debug` provider with iOS debug token
- **Production**: Uses `appAttestWithDeviceCheckFallback` provider
- **Native Setup**: Automatically configured in `AppDelegate.mm`

### Android Configuration

- **Development**: Uses `debug` provider with Android debug token
- **Production**: Uses `playIntegrity` provider
- **Native Setup**: Automatically configured through React Native Firebase

## Troubleshooting

### Common Issues

1. **App Check not initialized**
   - Check console logs for initialization errors
   - Verify Firebase configuration is correct
   - Ensure React Native Firebase App Check is properly installed

2. **Token retrieval fails with "API request error"**
   - **Most common cause**: Debug token not registered in Firebase Console
   - **Steps to fix**:
     1. Go to Firebase Console > Project Settings > App Check
     2. Find your iOS/Android app
     3. Click "Manage debug tokens"
     4. Verify your debug token is listed and active
     5. If not listed, click "Add debug token" and paste your token
   - **Other causes**:
     - Network connectivity issues
     - Firebase project configuration mismatch
     - Debug token expired or invalid

3. **Functions calls fail with App Check errors**
   - Verify App Check is initialized before calling functions
   - Check that your Cloud Functions have `enforceAppCheck: true`
   - Ensure debug tokens are properly configured

4. **Deprecation warnings**
   - These are expected during migration to v22
   - Warnings are silenced in the current configuration
   - They don't affect functionality

### Debug Commands

Use these utility functions for debugging:

```javascript
import { 
  logAppCheckStatus, 
  testAppCheckToken, 
  printDebugTokenInstructions 
} from '@/utils/appCheckUtils';

// Get comprehensive status
await logAppCheckStatus();

// Test token functionality
await testAppCheckToken();

// Get platform-specific setup instructions
printDebugTokenInstructions();
```

## Production Considerations

### iOS Production

- Uses **App Attest** with **Device Check** fallback
- No additional configuration needed
- Automatically enabled for App Store builds

### Android Production

- Uses **Play Integrity API**
- Requires app to be uploaded to Google Play Console
- Works with internal testing, closed testing, and production

### Security Notes

- Debug tokens should **never** be used in production
- Debug tokens should be **removed** from Firebase Console when not needed
- Consider rotating debug tokens periodically for security

## File Structure

```
config/
  └── firebaseConfig.js          # Main App Check configuration
utils/
  └── appCheckUtils.ts           # Debugging utilities
docs/
  └── APP_CHECK_SETUP.md         # This guide
```

## Next Steps

1. Replace the debug tokens in `config/firebaseConfig.js` with your actual tokens
2. Add those tokens to Firebase Console
3. Test on both iOS simulator and Android emulator
4. Verify Cloud Functions calls work correctly
5. Test production builds before release
