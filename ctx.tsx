import {createContext, type PropsWithChildren, useContext, useEffect} from 'react';
import {useStorageState} from './hooks/useStorageState';
import {setupPushNotifications, configurePushNotifications, isPhysicalDevice} from './utils/pushNotifications';

const AuthContext = createContext<{
    signIn: (user: any, token: string) => void;
    signOut: (user: any) => void;
    signUp: (user: any, token: string) => boolean;
    session?: string | null;
    isLoading: boolean;
}>({
    signIn: () => null,
    signUp: () => false,
    signOut: () => null,
    session: null,
    isLoading: false,
});

// This hook can be used to access the user info.
export function useSession() {
    const value = useContext(AuthContext);
    if (process.env.NODE_ENV !== 'production') {
        if (!value) {
            throw new Error(
                'useSession must be wrapped in a <SessionProvider />',
            );
        }
    }

    return value;
}

export function SessionProvider({children}: PropsWithChildren) {
    const [[isLoading, session], setSession] = useStorageState('session');

    // Configure push notifications when app starts
    useEffect(() => {
        try {
            // Only set up push notifications on physical devices
            if (isPhysicalDevice()) {
                // Set up how notifications should be handled when app is in foreground
                configurePushNotifications();
                
                // If user is already logged in, set up push notifications
                if (session) {
                    setupPushNotifications().catch(error => {
                        console.error('Failed to set up push notifications:', error);
                    });
                }
            } else {
                console.log('Skipping push notification setup on non-physical device');
            }
        } catch (error) {
            console.error('Error configuring session-level push notifications:', error);
        }
    }, [session]);

    return (
        <AuthContext.Provider
            value={{
                signIn: (user, token = 'xxx') => {
                    // Perform sign-in logic here
                    setSession(token);
                    
                    // Set up push notifications after signing in, but only on physical devices
                    if (isPhysicalDevice()) {
                        try {
                            setupPushNotifications().catch(error => {
                                console.error('Failed to set up push notifications after sign in:', error);
                            });
                        } catch (error) {
                            console.error('Error in push notification setup after sign in:', error);
                        }
                    }
                },
                signOut: user => {
                    setSession(null);
                },
                signUp: (user, token = 'xxx') => {
                    // Perform sign-up logic here
                    setSession(token);
                    
                    // Set up push notifications after signing up, but only on physical devices
                    if (isPhysicalDevice()) {
                        try {
                            setupPushNotifications().catch(error => {
                                console.error('Failed to set up push notifications after sign up:', error);
                            });
                        } catch (error) {
                            console.error('Error in push notification setup after sign up:', error);
                        }
                    }
                    
                    return true;
                },
                session,
                isLoading,
            }}>
            {children}
        </AuthContext.Provider>
    );
}
